{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    // types 字段用于指定需要包含的类型定义文件
    "types": [
      // node: 包含 Node.js 的类型定义，使得可以使用 Node.js 的 API 时有类型提示
      "node",
      // unplugin-icons/types/vue: 包含 unplugin-icons 插件的 Vue 相关类型定义
      // 这使得在 Vue 组件中使用该图标插件时有正确的类型提示和智能补全
      "unplugin-icons/types/vue"
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
