/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    404Page: typeof import('./src/views/error/404Page.vue')['default']
    AAffix: typeof import('@arco-design/web-vue')['Affix']
    AAutoComplete: typeof import('@arco-design/web-vue')['AutoComplete']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACol: typeof import('@arco-design/web-vue')['Col']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADescriptionsItem: typeof import('@arco-design/web-vue')['DescriptionsItem']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    AInputSearch: typeof import('@arco-design/web-vue')['InputSearch']
    AInputTag: typeof import('@arco-design/web-vue')['InputTag']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutHeader: typeof import('@arco-design/web-vue')['LayoutHeader']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AllTicket: typeof import('./src/views/ticket/AllTicket.vue')['default']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    Announcement: typeof import('./src/views/system/Announcement.vue')['default']
    AOption: typeof import('@arco-design/web-vue')['Option']
    ApiCenter: typeof import('./src/views/system/ApiCenter.vue')['default']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    AProgress: typeof import('@arco-design/web-vue')['Progress']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    AResult: typeof import('@arco-design/web-vue')['Result']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    AStatistic: typeof import('@arco-design/web-vue')['Statistic']
    AStep: typeof import('@arco-design/web-vue')['Step']
    ASteps: typeof import('@arco-design/web-vue')['Steps']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATimeline: typeof import('@arco-design/web-vue')['Timeline']
    ATimelineItem: typeof import('@arco-design/web-vue')['TimelineItem']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATransfer: typeof import('@arco-design/web-vue')['Transfer']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATrigger: typeof import('@arco-design/web-vue')['Trigger']
    ATypographyParagraph: typeof import('@arco-design/web-vue')['TypographyParagraph']
    ATypographyText: typeof import('@arco-design/web-vue')['TypographyText']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    AWatermark: typeof import('@arco-design/web-vue')['Watermark']
    BashAudit: typeof import('./src/views/audit/bashAudit.vue')['default']
    CreateTicket: typeof import('./src/views/ticket/CreateTicket.vue')['default']
    Dashboard: typeof import('./src/views/dashboard/index.vue')['default']
    Dataview: typeof import('./src/views/dataview/dataview.vue')['default']
    GroupCenter: typeof import('./src/views/system/GroupCenter.vue')['default']
    Hardware: typeof import('./src/views/assets/Hardware.vue')['default']
    HostDetail: typeof import('./src/views/assets/hostDetail.vue')['default']
    Hosts: typeof import('./src/views/assets/hosts.vue')['default']
    IconAkarIconsFile: typeof import('~icons/akar-icons/file')['default']
    IconBxReset: typeof import('~icons/bx/reset')['default']
    IconBxsUser: typeof import('~icons/bxs/user')['default']
    IconCarbonEnterprise: typeof import('~icons/carbon/enterprise')['default']
    IconCiClock: typeof import('~icons/ci/clock')['default']
    IconClarityHardDiskSolid: typeof import('~icons/clarity/hard-disk-solid')['default']
    IconFa6SolidServer: typeof import('~icons/fa6-solid/server')['default']
    IconFluentMdl2WaitlistConfirm: typeof import('~icons/fluent-mdl2/waitlist-confirm')['default']
    IconGrommetIconsStatusGoodSmall: typeof import('~icons/grommet-icons/status-good-small')['default']
    IconIconamoonCloseBold: typeof import('~icons/iconamoon/close-bold')['default']
    IconIconamoonComment: typeof import('~icons/iconamoon/comment')['default']
    IconIcOutlineDone: typeof import('~icons/ic/outline-done')['default']
    IconIcOutlineDownloadDone: typeof import('~icons/ic/outline-download-done')['default']
    IconIxAddApplication: typeof import('~icons/ix/add-application')['default']
    IconLetsIconsDoneAllRound: typeof import('~icons/lets-icons/done-all-round')['default']
    IconLucideLandPlot: typeof import('~icons/lucide/land-plot')['default']
    IconMageLockFill: typeof import('~icons/mage/lock-fill')['default']
    IconMaterialSymbolsAdd: typeof import('~icons/material-symbols/add')['default']
    IconMaterialSymbolsAddRounded: typeof import('~icons/material-symbols/add-rounded')['default']
    IconMaterialSymbolsCheckRounded: typeof import('~icons/material-symbols/check-rounded')['default']
    IconMaterialSymbolsCheckSmall: typeof import('~icons/material-symbols/check-small')['default']
    IconMaterialSymbolsCloseRounded: typeof import('~icons/material-symbols/close-rounded')['default']
    IconMaterialSymbolsDelete: typeof import('~icons/material-symbols/delete')['default']
    IconMaterialSymbolsDownload: typeof import('~icons/material-symbols/download')['default']
    IconMaterialSymbolsLogout: typeof import('~icons/material-symbols/logout')['default']
    IconMaterialSymbolsPendingActions: typeof import('~icons/material-symbols/pending-actions')['default']
    IconMaterialSymbolsRefreshRounded: typeof import('~icons/material-symbols/refresh-rounded')['default']
    IconMaterialSymbolsSearch: typeof import('~icons/material-symbols/search')['default']
    IconMaterialSymbolsUploadSharp: typeof import('~icons/material-symbols/upload-sharp')['default']
    IconMdiArrowBackCircle: typeof import('~icons/mdi/arrow-back-circle')['default']
    IconMdiCentos: typeof import('~icons/mdi/centos')['default']
    IconMdiCheckBold: typeof import('~icons/mdi/check-bold')['default']
    IconMdiFilterOutline: typeof import('~icons/mdi/filter-outline')['default']
    IconMdiGithub: typeof import('~icons/mdi/github')['default']
    IconMdiLinux: typeof import('~icons/mdi/linux')['default']
    IconMdiMicrosoftWindows: typeof import('~icons/mdi/microsoft-windows')['default']
    IconMdiOffline: typeof import('~icons/mdi/offline')['default']
    IconMdiProgressCheck: typeof import('~icons/mdi/progress-check')['default']
    IconMdiRedhat: typeof import('~icons/mdi/redhat')['default']
    IconMdiSummation: typeof import('~icons/mdi/summation')['default']
    IconMdiUbuntu: typeof import('~icons/mdi/ubuntu')['default']
    IconMingcuteAddFill: typeof import('~icons/mingcute/add-fill')['default']
    IconMingcuteAnnouncementLine: typeof import('~icons/mingcute/announcement-line')['default']
    IconMingcuteSafeFlashFill: typeof import('~icons/mingcute/safe-flash-fill')['default']
    IconMingcuteShopLine: typeof import('~icons/mingcute/shop-line')['default']
    IconPepiconsPopClock: typeof import('~icons/pepicons-pop/clock')['default']
    IconPhNetworkBold: typeof import('~icons/ph/network-bold')['default']
    IconRiDragMove2Fill: typeof import('~icons/ri/drag-move2-fill')['default']
    IconStreamlineDownloadCircleSolid: typeof import('~icons/streamline/download-circle-solid')['default']
    IconStreamlineUploadCircleSolid: typeof import('~icons/streamline/upload-circle-solid')['default']
    IconSvgAnolis: typeof import('~icons/svg/anolis')['default']
    IconSvgKylin: typeof import('~icons/svg/kylin')['default']
    IconSvgLoginLeft: typeof import('~icons/svg/login-left')['default']
    IconSvgLoginLogo: typeof import('~icons/svg/login-logo')['default']
    IconSvgLogo: typeof import('~icons/svg/logo')['default']
    IconSvgOpenEuler: typeof import('~icons/svg/open-euler')['default']
    IconTablerEdit: typeof import('~icons/tabler/edit')['default']
    IconTablerNetwork: typeof import('~icons/tabler/network')['default']
    IconTablerPlayerPause: typeof import('~icons/tabler/player-pause')['default']
    IconTablerPlayerPlay: typeof import('~icons/tabler/player-play')['default']
    IconTablerRefresh: typeof import('~icons/tabler/refresh')['default']
    IconTablerServerCog: typeof import('~icons/tabler/server-cog')['default']
    IconUilSetting: typeof import('~icons/uil/setting')['default']
    IconUiwLinux: typeof import('~icons/uiw/linux')['default']
    IconWpfStatistics: typeof import('~icons/wpf/statistics')['default']
    IdentifyCode: typeof import('./src/views/login/components/IdentifyCode.vue')['default']
    Ipaddress: typeof import('./src/views/assets/ipaddress.vue')['default']
    Login: typeof import('./src/views/login/index.vue')['default']
    MenuCenter: typeof import('./src/views/system/MenuCenter.vue')['default']
    MyTicket: typeof import('./src/views/ticket/MyTicket.vue')['default']
    RoleCenter: typeof import('./src/views/system/RoleCenter.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SettingCenter: typeof import('./src/views/system/SettingCenter.vue')['default']
    TicketList: typeof import('./src/views/ticket/components/TicketList.vue')['default']
    TicketTimeline: typeof import('./src/components/TicketTimeline.vue')['default']
    UserCenter: typeof import('./src/views/system/UserCenter.vue')['default']
  }
}
