{"name": "dunshan-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@types/lodash": "^4.17.13", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@vueuse/core": "^12.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.9", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.2.6", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-data-ui": "^2.4.63", "vue-echarts": "^7.0.3", "vue-router": "^4.4.5"}, "devDependencies": {"@arco-design/web-vue": "^2.56.3", "@iconify-json/akar-icons": "^1.2.2", "@iconify-json/bx": "^1.2.2", "@iconify-json/bxs": "^1.2.2", "@iconify-json/carbon": "^1.2.6", "@iconify-json/ci": "^1.2.2", "@iconify-json/clarity": "^1.2.2", "@iconify-json/dashicons": "^1.2.1", "@iconify-json/fa6-solid": "^1.2.3", "@iconify-json/fluent": "^1.2.11", "@iconify-json/fluent-mdl2": "^1.2.1", "@iconify-json/gridicons": "^1.2.2", "@iconify-json/grommet-icons": "^1.2.1", "@iconify-json/hugeicons": "^1.2.3", "@iconify-json/ic": "^1.2.2", "@iconify-json/icon-park": "^1.2.2", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/icon-park-solid": "^1.2.2", "@iconify-json/iconamoon": "^1.2.2", "@iconify-json/ix": "^1.2.1", "@iconify-json/lets-icons": "^1.2.1", "@iconify-json/line-md": "^1.2.4", "@iconify-json/logos": "^1.2.4", "@iconify-json/lsicon": "^1.2.3", "@iconify-json/lucide": "^1.2.18", "@iconify-json/mage": "^1.2.2", "@iconify-json/material-symbols": "^1.2.10", "@iconify-json/material-symbols-light": "^1.2.12", "@iconify-json/mdi": "^1.2.1", "@iconify-json/mi": "^1.2.2", "@iconify-json/mingcute": "^1.2.1", "@iconify-json/mynaui": "^1.2.9", "@iconify-json/oui": "^1.2.4", "@iconify-json/pepicons-pop": "^1.2.1", "@iconify-json/ph": "^1.2.2", "@iconify-json/proicons": "^1.2.12", "@iconify-json/ri": "^1.2.5", "@iconify-json/solar": "^1.2.2", "@iconify-json/streamline": "^1.2.2", "@iconify-json/tabler": "^1.2.13", "@iconify-json/uil": "^1.2.3", "@iconify-json/uiw": "^1.2.1", "@iconify-json/vaadin": "^1.2.1", "@iconify-json/weui": "^1.2.2", "@iconify-json/wpf": "^1.2.0", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.9.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.1", "pinia-plugin-persistedstate": "^4.2.0", "prettier": "^3.3.3", "sass": "^1.83.0", "typescript": "~5.6.3", "unplugin-auto-import": "^0.19.0", "unplugin-icons": "^0.22.0", "unplugin-vue-components": "^0.28.0", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.5", "vue-tsc": "^2.1.10"}}