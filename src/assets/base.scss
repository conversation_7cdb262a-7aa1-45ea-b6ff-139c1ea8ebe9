@import './normalize.css';

/* 路由过度动画 */
.fade-x {
  &-enter-active {
    transition: all 0.3s ease-out;
  }

  &-leave-active {
    transition: all 0;
  }

  &-enter-from {
    transform: translateX(20px);
    opacity: 0;
  }

  &-leave-from {
    opacity: 0;
  }
}

.general-card {
  border-radius: 4px;
  border: none;

  > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }

  > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 加载中遮罩
.spin-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

body[arco-theme='dark'] {
  --w-e-textarea-bg-color: var(--color-bg-2);
  --w-e-textarea-color: var(--color-text-1);
}
