import IconMaterialSymbolsWbSunnyRounded from '~icons/material-symbols/wb-sunny-rounded'
import IconMaterialSymbolsDarkModeRounded from '~icons/material-symbols/dark-mode-rounded'
import IconMaterialSymbolsComputer from '~icons/material-symbols/computer'
import { useColorMode } from '@vueuse/core'
import { defineStore } from 'pinia'
import { markRaw, ref, watch } from 'vue'

import type { ThemeMode } from './type'

const useSystemStore = defineStore(
  'system',
  () => {
    // 当前可切换布局
    const currentSwitchlayout = shallowRef<any>(null)

    // 可切换布局列表
    const switchLayoutList = shallowRef<any>([])

    const initSwitchLayout = (list: any[]) => {
      if (list && list.length > 0) {
        switchLayoutList.value = [...list]
        const matchedLayout = switchLayoutList.value.find(
          (item: any) => item.name === currentSwitchlayout.value?.name
        )
        currentSwitchlayout.value = matchedLayout || switchLayoutList.value[0]
      }
    }

    // 主题模式列表
    const modeList = ref([
      {
        name: 'auto',
        icon: markRaw(IconMaterialSymbolsComputer),
        title: '自动模式'
      },
      {
        name: 'light',
        icon: markRaw(IconMaterialSymbolsWbSunnyRounded),
        title: '亮色模式'
      },
      {
        name: 'dark',
        icon: markRaw(IconMaterialSymbolsDarkModeRounded),
        title: '暗色模式'
      }
    ])

    // 当前模式
    const currentMode = ref<any>(null)
    const mode = useColorMode({
      attribute: 'arco-theme',
      emitAuto: true,
      selector: 'body',
      initialValue: currentMode.value?.name,
      storageKey: null
    })
    watchEffect(() => (mode.value = currentMode.value?.name))

    // 初始化模式
    const initMode = () => {
      if (!currentMode.value) {
        currentMode.value = modeList.value[0]
      } else {
        currentMode.value = modeList.value.find(
          item => item.name === currentMode.value.name
        )
      }
    }

    // 判断是否为暗色模式的方法
    const isDarkMode = computed(() => {
      return (
        currentMode.value?.name === 'dark' ||
        (currentMode.value?.name === 'auto' &&
          window.matchMedia('(prefers-color-scheme: dark)').matches)
      )
    })

    return {
      currentMode,
      modeList,
      isDarkMode,
      initMode
    }
  },
  {
    persist: {
      key: 'system-store',
      pick: ['currentMode']
    }
  }
)

export default useSystemStore
