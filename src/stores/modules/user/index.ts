import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import { constantRoutesFormat, anyRoutes } from '@/router/constantRoutes'
import { dynamicRoutesFormat } from '@/router/dynamicRoutes'
import type { LoginForm } from '@/types/login'
import { reqLogin } from '@/api/login'
import router from '@/router'
import type { UserInfo } from '@/types/user'
import { reqGetUserInfo } from '@/api/user'

// 过滤路由的函数
const filterAsyncRoutes = (
  routes: RouteRecordRaw[],
  permissions: string[]
): RouteRecordRaw[] => {
  return routes
    .map(route => {
      // 创建路由副本
      const newRoute = { ...route }

      // 如果有子路由，过滤子路由
      if (newRoute.children) {
        // 只保留有权限的子路由
        newRoute.children = newRoute.children.filter(
          childRoute =>
            childRoute.name && permissions.includes(childRoute.name as string)
        )
      }

      return newRoute
    })
    .filter(route => {
      // 保留有子路由的父路由
      if (route.children && route.children.length > 0) {
        return true
      }

      // 保留有权限的叶子节点
      return route.name && permissions.includes(route.name as string)
    }) as RouteRecordRaw[]
}

const useUserStore: any = defineStore(
  'user',
  () => {
    const menuList = ref<RouteRecordRaw[]>(constantRoutesFormat)
    const token = ref<string>('')
    const userInfo = ref<UserInfo | null>(null)

    const Login = async (loginForm: LoginForm) => {
      try {
        const res = await reqLogin(loginForm)
        token.value = res.data.token
        return 'ok'
      } catch (error: any) {
        return Promise.reject(error)
      }
    }

    const clearUserInfo = () => {
      token.value = ''
      userInfo.value = null
      menuList.value = constantRoutesFormat
    }

    const Logout = () => {
      // 清除动态添加的路由
      // router.getRoutes().forEach(route => {
      //   if (
      //     route.name &&
      //     !constantRoutesFormat.some(r => r.name === route.name)
      //   ) {
      //     router.removeRoute(route.name)
      //   }
      // })

      // 清除用户信息
      clearUserInfo()

      // 跳转到登录页
      router.push('/login')
    }

    const GetUserInfo = async () => {
      try {
        const res = await reqGetUserInfo()
        userInfo.value = res.data

        // 添加超级管理员判断
        let accessRoutes: RouteRecordRaw[]
        if (userInfo.value.role_names?.includes('超级管理员')) {
          // 超级管理员获得所有动态路由
          accessRoutes = dynamicRoutesFormat
        } else {
          // 其他用户按权限过滤路由
          accessRoutes = filterAsyncRoutes(
            dynamicRoutesFormat,
            userInfo.value.permissions
          )
        }

        // 添加过滤后的路由
        accessRoutes.forEach(route => {
          router.addRoute(route)
        })

        // 添加 404 路由
        if (!router.hasRoute(anyRoutes.name as string)) {
          router.addRoute(anyRoutes)
        }

        // 更新菜单列表
        menuList.value = [...constantRoutesFormat, ...accessRoutes, anyRoutes]

        return 'ok'
      } catch (error: any) {
        return Promise.reject(error)
      }
    }

    return {
      menuList,
      token,
      userInfo,
      Login,
      Logout,
      GetUserInfo,
      clearUserInfo
    }
  },
  {
    persist: {
      key: 'user-store',
      pick: ['token']
    }
  }
)

export default useUserStore
