<template>
  <div class="login-container">
    <div class="login-left">
      <div class="logo-image-container">
        <icon-svg-login-logo class="logo-image" />
      </div>
      <div class="logo-server-container">
        <icon-svg-login-left class="logo-server" />
      </div>
    </div>
    <div class="login-right">
      <div class="title">盾山-运维管理平台</div>
      <p>欢迎回来，请输入您的凭证进行登录。</p>
      <a-form
        ref="loginFormRef"
        :model="loginForm"
        :style="{ width: '380px' }"
        :rules="rules"
        @submit="handleSubmit"
      >
        <a-form-item
          field="loginname"
          validate-trigger="blur"
          hide-label
          hide-asterisk
        >
          <a-input v-model="loginForm.loginname" placeholder="请输入登录名">
            <template #prefix>
              <icon-bxs-user />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item
          field="password"
          validate-trigger="blur"
          hide-label
          hide-asterisk
        >
          <a-input-password
            v-model="loginForm.password"
            placeholder="请输入密码"
          >
            <template #prefix>
              <icon-mage-lock-fill />
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item
          field="code"
          validate-trigger="blur"
          hide-label
          hide-asterisk
        >
          <a-input
            v-model="loginForm.code"
            placeholder="请输入验证码"
            :style="{ marginRight: '5px' }"
          >
            <template #prefix>
              <icon-mingcute-safe-flash-fill />
            </template>
          </a-input>
          <Identify :identify-code="identifyCode" @click="refreshCode" />
        </a-form-item>
        <a-form-item hide-label>
          <a-button
            type="primary"
            long
            html-type="submit"
            :loading="login_loading"
            >登陆</a-button
          >
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import Identify from './components/IdentifyCode.vue'
import { useUserStore } from '@/stores'

const login_loading = ref(false)

// 定义 loginForm 的类型
const loginFormRef = ref()
const router = useRouter()

const userStore = useUserStore()

const loginForm = ref({
  loginname: '',
  password: '',
  code: ''
})

// 校验规则
const rules = ref({
  loginname: [{ required: true, message: '请输入登录名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        if (!value) {
          callback('请输入验证码')
          return
        }
        if (value.toLowerCase() !== identifyCode.value.toLowerCase()) {
          callback('验证码错误')
          // refreshCode() // 验证码错误时刷新
          return
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
})

// 验证码
const identifyCode = ref('')
const identifyCodes = '234567890abcdefjhjknopqrsduvwxyz'

// 生成随机数函数
const randomNum = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min) + min)
}

// 生成验证码
const makeCode = (length: number) => {
  let code = ''
  for (let i = 0; i < length; i++) {
    code += identifyCodes.charAt(randomNum(0, identifyCodes.length))
  }
  identifyCode.value = code
}

// 重置验证码
const refreshCode = () => {
  makeCode(4) // 直接传入长度参数
}

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  const validResult = await loginFormRef.value.validate()
  if (validResult) {
    return
  }

  login_loading.value = true
  try {
    await userStore.Login({
      loginname: loginForm.value.loginname,
      password: loginForm.value.password
    })
    AMessage.success('登录成功')
    router.push('/')
  } catch (error: any) {
    AMessage.error(error.message)
  } finally {
    refreshCode()
    login_loading.value = false
  }
}

onMounted(() => {
  refreshCode()
})
</script>

<style scoped lang="scss">
.login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  background-color: var(--color-bg-1);
  overflow: hidden;
}

.login-left {
  width: 50%;
  height: 100vh;
  background-color: #f7fbfc;
  overflow: hidden;

  .logo-image-container {
    height: 20%;
    overflow: hidden;

    .logo-image {
      width: 180px;
      height: 100px;
      padding-left: 40px;
    }
  }

  .logo-server-container {
    height: 80%;
    display: flex;
    justify-content: center;
    align-items: center;

    .logo-server {
      width: 300px;
      height: 300px;
    }
  }
}

.login-right {
  width: 400px;
  margin: 0 auto;
  padding: 2rem;

  .title {
    font-size: 28px;
    font-weight: bold;
    color: var(--color-text-1);
    text-align: center;
  }

  p {
    font-size: 13px;
    color: var(--color-neutral-8);
    margin-bottom: 40px;
    text-align: center;
  }
}
</style>
