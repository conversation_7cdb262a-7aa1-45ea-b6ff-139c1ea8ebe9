<script setup lang="ts">
import type { BashAuditRecord, BashAuditRecordQuery } from '@/types/bash_audit'
import { reqGetBashAuditList } from '@/api/bashAudit'
// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<BashAuditRecordQuery>({
  page: 1,
  page_size: 10
})

// 定义危险命令列表
const dangerousCommands = [
  'rm',
  'mv',
  'dd',
  'mkfs',
  'format',
  'shutdown',
  'reboot',
  ':(){:|:&};:', // fork炸弹
  'chmod',
  '>/dev/sda',
  'wget',
  'curl',
  '>${', // 重定向到特殊文件
  '>/', // 重定向到根目录
  'sudo'
]

// 检查命令是否包含危险操作
const isDangerousCommand = (command: string): boolean => {
  return dangerousCommands.some(dangerous =>
    new RegExp(`\\b${dangerous}\\b`).test(command.toLowerCase())
  )
}

// 表格列配置
const columns = [
  {
    title: '时间',
    dataIndex: 'timestamp',
    width: 180
  },
  {
    title: '主机名',
    dataIndex: 'host_name',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '主机IP',
    dataIndex: 'host_ip',
    width: 140
  },
  {
    title: '源IP',
    dataIndex: 'source_ip',
    width: 140
  },
  {
    title: '用户名',
    dataIndex: 'username',
    width: 170,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '系统用户',
    dataIndex: 'system_user',
    width: 100
  },
  {
    title: '终端',
    dataIndex: 'terminal',
    width: 100
  },
  {
    title: '工作目录',
    dataIndex: 'working_directory',
    ellipsis: true,
    tooltip: true,
    width: 150
  },

  {
    title: '执行命令',
    dataIndex: 'executed_command',
    width: 300,
    ellipsis: true,
    tooltip: true,
    slotName: 'executed_command'
  }
]

// 加载状态标识
const loading = ref(false)
// 数据总条数
const total = ref(0)
// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 审计记录列表数据
const auditList = ref<BashAuditRecord[]>([])

loading.value = true

// 获取审计记录列表数据
const getAuditList = async () => {
  try {
    const res = await reqGetBashAuditList(params)
    auditList.value = res.data.bash_audits
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取审计记录失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const onPageChange = (current: number) => {
  params.page = current
  getAuditList()
}

// 每页条数变化处理
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getAuditList()
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getAuditList()
}

// 添加定时器引用
const timer = ref<NodeJS.Timeout | null>(null)

// 启动自动更新
const startAutoUpdate = () => {
  // 清除可能存在的旧定时器
  if (timer.value) {
    clearInterval(timer.value)
  }
  // 设置新的定时器，每秒更新一次
  timer.value = setInterval(() => {
    getAuditList()
  }, 3000)
}

// 停止自动更新
const stopAutoUpdate = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// 组件挂载时启动自动更新
onMounted(() => {
  getAuditList()
  startAutoUpdate()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="BASH审计">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="开始时间" field="start_time">
                  <a-date-picker
                    v-model="params.start_time"
                    placeholder="请选择开始时间"
                    style="width: 100%"
                    show-time
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="结束时间" field="end_time">
                  <a-date-picker
                    v-model="params.end_time"
                    placeholder="请选择结束时间"
                    style="width: 100%"
                    show-time
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="主机名" field="host_name">
                  <a-input
                    v-model="params.host_name"
                    placeholder="请输入主机名"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="主机IP" field="host_ip">
                  <a-input
                    v-model="params.host_ip"
                    placeholder="请输入主机IP"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="用户名" field="username">
                  <a-input
                    v-model="params.username"
                    placeholder="请输入用户名"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="执行命令" field="executed_command">
                  <a-input
                    v-model="params.executed_command"
                    placeholder="请输入执行命令"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="getAuditList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col
          :span="24"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip :content="timer ? '关闭自动更新' : '开启自动更新'">
            <div
              class="action-icon"
              @click="timer ? stopAutoUpdate() : startAutoUpdate()"
            >
              <icon-tabler-player-play v-if="!timer" />
              <icon-tabler-player-pause v-else />
            </div>
          </a-tooltip>
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getAuditList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="auditList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #executed_command="{ record }">
          <span
            :class="{
              'dangerous-text': isDangerousCommand(record.executed_command)
            }"
          >
            {{ record.executed_command }}
          </span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.dangerous-text {
  color: rgb(var(--red-7));
  font-weight: 500;
}
</style>
