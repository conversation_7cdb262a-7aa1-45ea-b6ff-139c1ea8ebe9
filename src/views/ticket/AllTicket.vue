<script lang="ts" setup>
import type { TicketRecord, TicketListParams } from '@/types/ticket'
import type { TableColumnData } from '@arco-design/web-vue'
import {
  reqGetTicketList,
  reqDeleteTicket,
  reqDownloadAttachment
} from '@/api/ticket'
import { ticketTypeConfig } from '@/types/ticket'
import TicketTimeline from './components/TicketTimeline.vue'
import type { Attachment } from '@/types/ticket'

// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<TicketListParams>({
  page: 1,
  page_size: 10,
  title: '',
  type: '',
  progress: '',
  submitter: '',
  assignee: ''
})

// 表格列配置
const columns: TableColumnData[] = [
  {
    title: '工单标题',
    dataIndex: 'title',
    width: 220,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '工单类型',
    dataIndex: 'type',
    width: 120,
    slotName: 'type',
    tooltip: true,
    ellipsis: true
  },
  {
    title: '提交人',
    dataIndex: 'submitter',
    width: 140,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '指派人',
    dataIndex: 'assignee',
    width: 140,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '当前进度',
    dataIndex: 'progress',
    width: 120,
    slotName: 'progress'
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    width: 180
  },
  {
    title: '完成时间',
    dataIndex: 'finish_time',
    width: 180
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 150,
    fixed: 'right'
  }
]

// 进度选项
const progressOptions = [
  { label: '审批中', value: '2' },
  { label: '处理中', value: '3' },
  { label: '已完成', value: '4' },
  { label: '已拒绝', value: '5' },
  { label: '已撤销', value: '6' }
]

// 工单类型选项
const typeOptions = Object.values(ticketTypeConfig).map(item => ({
  label: item.label,
  value: item.value
}))

// 加载状态
const loading = ref(false)
// 数据总条数
const total = ref(0)
// 工单列表数据
const ticketList = ref<TicketRecord[]>([])

// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 抽屉显示状态
const drawerVisible = ref(false)
// 当前查看的工单详情
const currentTicket = ref<TicketRecord | null>(null)

// 添加加载状态
const downloadingFiles = ref<Set<string>>(new Set())

// 获取工单列表
const getTicketList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const res = await reqGetTicketList(params)
    if (res.code === 200) {
      ticketList.value = res.data.tickets
      total.value = res.data.total
    } else {
      AMessage.error(res.message || '获取工单列表失败')
    }
  } catch (error: any) {
    AMessage.error(error.message || '获取工单列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const onPageChange = (current: number) => {
  params.page = current
  getTicketList()
}

// 每页条数变化处理
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getTicketList()
}

// 查看工单详情
const handleViewDetail = (record: TicketRecord) => {
  currentTicket.value = record
  drawerVisible.value = true
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
  currentTicket.value = null
}

// 删除工单
const handleDelete = async (record: TicketRecord) => {
  if (!record.id) return
  try {
    loading.value = true
    await reqDeleteTicket(record.id)
    AMessage.success('删除工单成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除工单失败')
  } finally {
    loading.value = false
    getTicketList()
  }
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getTicketList()
}

// 组件挂载时获取列表
onMounted(() => {
  getTicketList()
})

// 状态配置
const statusConfig = {
  '1': {
    color: '#165DFF',
    bgColor: '#E8F3FF',
    text: '待处理'
  },
  '2': {
    color: '#FF7D00',
    bgColor: '#FFF3E8',
    text: '审批中'
  },
  '3': {
    color: '#FF7D00',
    bgColor: '#FFF3E8',
    text: '处理中'
  },
  '4': {
    color: '#00B42A',
    bgColor: '#E8FFEA',
    text: '已完成'
  },
  '5': {
    color: '#F53F3F',
    bgColor: '#FFE8E8',
    text: '已拒绝'
  },
  '6': {
    color: '#86909C',
    bgColor: '#F2F3F5',
    text: '已撤销'
  }
}

// 获取工单状态文本
const getStatusText = (progress: string) => {
  return statusConfig[progress as keyof typeof statusConfig]?.text || '未知状态'
}

// 获取状态样式
const getStatusStyle = (progress: string) => {
  const status = statusConfig[progress as keyof typeof statusConfig]
  return {
    backgroundColor: status?.bgColor || '',
    color: status?.color || ''
  }
}

// 获取工单类型文本
const getTicketTypeText = (type: string) => {
  return ticketTypeConfig[type as keyof typeof ticketTypeConfig]?.label || type
}

// 添加下载附件的处理函数
const handleDownloadAttachment = async (file: Attachment) => {
  // 如果正在下载，则不处理
  if (downloadingFiles.value.has(file.file_name)) {
    return
  }

  try {
    // 添加到下载集合
    downloadingFiles.value.add(file.file_name)

    const response = await reqDownloadAttachment(file.src, file.file_name)

    const urlObject = window.URL || window.webkitURL
    const blob = new Blob([response.data], { type: 'application/octet-stream' })
    const downloadUrl = urlObject.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = file.file_name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(downloadUrl)
    AMessage.success('下载成功')
  } catch (error: any) {
    AMessage.error('下载文件失败')
  } finally {
    // 从下载集合中移除
    downloadingFiles.value.delete(file.file_name)
  }
}
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="所有工单">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="工单标题" field="title">
                  <a-input
                    v-model="params.title"
                    placeholder="请输入工单标题"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="工单类型" field="type">
                  <a-select
                    v-model="params.type"
                    placeholder="请选择工单类型"
                    allow-clear
                    :options="typeOptions"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="当前进度" field="progress">
                  <a-select
                    v-model="params.progress"
                    placeholder="请选择当前进度"
                    allow-clear
                    :options="progressOptions"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="提交人" field="submitter">
                  <a-input
                    v-model="params.submitter"
                    placeholder="请输入提交人"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="指派人" field="assignee">
                  <a-input
                    v-model="params.assignee"
                    placeholder="请输入指派人"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="getTicketList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <a-table
        :columns="columns"
        :data="ticketList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #progress="{ record }">
          <a-tag :style="getStatusStyle(record.progress)">
            {{ getStatusText(record.progress) }}
          </a-tag>
        </template>
        <template #type="{ record }">
          {{ getTicketTypeText(record.type) }}
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="handleViewDetail(record)">
              详情
            </a-button>
            <a-popconfirm
              :content="`确定要删除该工单吗？`"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 工单详情抽屉 -->
    <a-drawer
      :visible="drawerVisible"
      @cancel="closeDrawer"
      unmountOnClose
      :width="600"
      :footer="false"
      :closable="false"
      title="工单详情"
    >
      <template v-if="currentTicket">
        <div class="action-area-box">
          <div class="detail-header">
            <div class="title-row">
              <h2 class="title">{{ currentTicket?.title }}</h2>
              <a-tag :style="getStatusStyle(currentTicket?.progress || '')">
                {{ getStatusText(currentTicket?.progress || '') }}
              </a-tag>
            </div>
            <div class="meta-info">
              <a-space>
                <span class="submitter">{{ currentTicket?.submitter }}</span>
                <span class="time">{{ currentTicket?.create_time }} 提交</span>
              </a-space>
            </div>
          </div>
          <a-divider />
          <div class="detail-content">
            <div class="section">
              <a-descriptions :column="1">
                <a-descriptions-item label="工单类型">
                  {{ getTicketTypeText(currentTicket?.type || '') }}
                </a-descriptions-item>
                <a-descriptions-item label="处理人">
                  {{ currentTicket?.assignee }}
                </a-descriptions-item>
                <a-descriptions-item label="工单描述">
                  <div v-html="currentTicket?.description"></div>
                </a-descriptions-item>
              </a-descriptions>
            </div>

            <div class="section" v-if="currentTicket?.attachments?.length">
              <h4 class="section-title">附件</h4>
              <div class="attachments">
                <a-space>
                  <a-tag
                    v-for="file in currentTicket?.attachments"
                    :key="file.file_name"
                    class="attachment-tag"
                    hoverable
                    :loading="downloadingFiles.has(file.file_name)"
                    @click="handleDownloadAttachment(file)"
                  >
                    <a-space>
                      <icon-akar-icons-file />
                      {{ file.file_name }}
                    </a-space>
                  </a-tag>
                </a-space>
              </div>
            </div>
          </div>
          <a-divider />
          <div class="section">
            <h3 class="section-title">处理进度</h3>
            <TicketTimeline :ticket="currentTicket" />
          </div>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.action-area-box {
  flex: 1;
  overflow: auto;
  padding: 0 10px;

  .detail-header {
    .title-row {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }
  }
}

.attachment-tag {
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: var(--color-fill-3);
  }

  &[loading] {
    cursor: not-allowed;
    opacity: 0.7;
  }
}
</style>
