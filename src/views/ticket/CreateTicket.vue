<script lang="ts" setup>
import type { UserRecord } from '@/types/user'
import { reqGetUserList } from '@/api/user'
import { reqGetGroupList } from '@/api/group'

import {
  reqCreateTicket,
  reqUploadAttachment,
  reqDeleteAttachment
} from '@/api/ticket'
import type { Attachment, TicketRecord } from '@/types/ticket'
import type {
  FileItem,
  RequestOption,
  UploadRequest
} from '@arco-design/web-vue'

import { onBeforeUnmount, ref, shallowRef, onMounted, reactive } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { useUserStore } from '@/stores'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()

const editorRef = shallowRef()
const toolbarConfig: any = {}
const editorConfig = {
  placeholder: '请输入内容...'
}
const jsonContent = [
  {
    type: 'paragraph',
    lineHeight: '1.5',
    children: [{ text: '', fontFamily: '微软雅黑', fontSize: '15px' }]
  }
]

toolbarConfig.excludeKeys = ['group-image', 'group-video']

const handleCreated = (editor: any) => {
  editorRef.value = editor
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

// 表单数据
const formData = ref<TicketRecord>({
  title: '',
  type: '',
  progress: '1',
  submitter: userStore.userInfo.username,
  assignee: '',
  attachments: [],
  description: ''
})

// 控制结果页显示
const showResult = ref(false)

// 获取指派用户
let assigneeList = reactive<UserRecord[]>([])
const fetAchssigneeList = async () => {
  const paramsGroup = { name: 'DEVOPS' }
  try {
    const groupRes = await reqGetGroupList({
      page: 1,
      page_size: 999,
      ...paramsGroup
    })

    const { data } = await reqGetUserList({
      page: 1,
      page_size: 999,
      groups: groupRes.data.groups[0]?.id
    })
    assigneeList = data.users
  } catch (err: any) {
    AMessage.error(err.message)
  }
}

// 表单规则
const rules = {
  title: [{ required: true, message: '请输入工单标题' }],
  type: [{ required: true, message: '请选择工单类型' }],
  assignee: [{ required: true, message: '请选择指派人' }]
}

// 文件列表
const fileList = ref<FileItem[]>([])

// 文件上传限制
const uploadConfig = {
  maxCount: 3,
  maxSize: 3 * 1024 * 1024, // 3MB
  accept: '.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx' // 明确的文件类型限制
}

// 处理文件上传前的验证
const beforeUpload = (file: File) => {
  // 检查文件类型
  const extension = file.name.split('.').pop()?.toLowerCase()
  const acceptedTypes = uploadConfig.accept
    .split(',')
    .map(type => type.replace('.', '').toLowerCase())

  if (!extension || !acceptedTypes.includes(extension)) {
    AMessage.error('不支持的文件类型')
    return false
  }

  // 检查文件数量
  if (fileList.value.length >= uploadConfig.maxCount) {
    AMessage.error(`最多只能上传 ${uploadConfig.maxCount} 个文件`)
    return false
  }

  // 检查文件大小
  if (file.size > uploadConfig.maxSize) {
    AMessage.error(`文件大小不能超过 ${uploadConfig.maxSize / 1024 / 1024}MB`)
    return false
  }

  return true
}

// 处理文件上传
const handleUpload = (options: RequestOption): UploadRequest => {
  const { onProgress, onError, onSuccess, fileItem } = options

  if (!fileItem.file) {
    onError(new Error('文件不存在'))
    return { abort: () => {} }
  }

  // 直接发送请求
  reqUploadAttachment(fileItem.file)
    .then(res => {
      if (res.code === 200) {
        // 添加到表单数据的附件列表
        const newAttachment: Attachment = {
          file_name: fileItem.name as string,
          src: res.data.src
        }
        formData.value.attachments.push(newAttachment)

        // 同步到 fileList
        fileList.value.push({
          uid: fileItem.uid,
          name: fileItem.name,
          url: res.data.src,
          status: 'done'
        })

        onSuccess()
      } else {
        onError(new Error(res.message))
      }
    })
    .catch(err => {
      onError(err)
    })

  return {
    abort: () => {
      // 取消上传的逻辑
    }
  }
}

// 处理文件删除
const handleRemove = async (file: FileItem) => {
  try {
    // 找到对应的附件
    const attachment = formData.value.attachments.find(
      item => item.file_name === file.name
    )
    if (!attachment) return true

    console.log('Deleting file:', {
      file,
      attachment,
      src: attachment.src
    })

    // 调用删除接口，传递文件路径
    const res = await reqDeleteAttachment(attachment.src)

    if (res.code === 200) {
      // 从表单数据中移除
      formData.value.attachments = formData.value.attachments.filter(
        item => item.file_name !== file.name
      )
      // 同步移除 fileList 中的文件
      fileList.value = fileList.value.filter(item => item.name !== file.name)
      return true
    } else {
      throw new Error(res.message)
    }
  } catch (err: any) {
    console.error('删除文件失败:', err)
    return false
  }
}

// 提交表单时包含附件
const handleSubmit = async () => {
  try {
    const res = await reqCreateTicket(formData.value)

    if (res.code === 200) {
      showResult.value = true
    }
  } catch (err: any) {
    AMessage.error(err.message || '提交失败')
  }
}

// 重置表单数据
const resetForm = () => {
  // 重置表单数据
  formData.value = {
    title: '',
    type: '',
    progress: '1',
    submitter: userStore.userInfo.username,
    assignee: '',
    attachments: [],
    description: ''
  }

  // 重置文件列表
  fileList.value = []

  // 重置编辑器内容
  if (editorRef.value) {
    editorRef.value.setHtml('')
  }
}

// 返回编辑
const handleBack = () => {
  showResult.value = false
  resetForm() // 调用重置函数
}

onMounted(() => {
  fetAchssigneeList()
})
</script>

<template>
  <div class="container">
    <a-card class="general-card">
      <!-- 成功结果页 -->
      <div v-if="showResult">
        <a-result
          status="success"
          title="工单提交成功"
          subtitle="工单已提交成功，请等待审批"
        >
          <!-- 添加时间轴 -->
          <div class="timeline-wrapper">
            <a-timeline>
              <a-timeline-item dotColor="#00B42A">
                开始
                <template #label>
                  {{ new Date().toLocaleString() }}
                </template>
              </a-timeline-item>

              <a-timeline-item>
                审批中
                <template #dot>
                  <Icon-ci-clock
                    :style="{ fontSize: '10px', color: 'rgb(var(--orange-5))' }"
                  />
                </template>

                <template #label> {{ formData.assignee }} 审批中 </template>
              </a-timeline-item>
              <a-timeline-item> 处理中 </a-timeline-item>
              <a-timeline-item> 结束 </a-timeline-item>
            </a-timeline>
          </div>
          <template #extra>
            <a-space>
              <a-button type="primary" @click="handleBack">继续创建</a-button>
              <a-button @click="router.push('/ticket/MyTicket')">
                查看工单
              </a-button>
            </a-space>
          </template>
        </a-result>
      </div>

      <!-- 表单页面 -->
      <template v-else>
        <!-- 进度条 -->
        <a-steps :current="1">
          <a-step title="填写工单" description="描述工单事件并提交" />
          <a-step title="审批中" description="等待指派人审批" />
          <a-step title="处理中" description="指派人处理中" />
          <a-step title="已完成" description="工单已完成" />
          <a-step title="已拒绝" description="工单已拒绝" />
        </a-steps>

        <a-divider />

        <!-- 表单内容 -->
        <a-form
          :model="formData"
          :rules="rules"
          @submit="handleSubmit"
          :label-col-props="{ span: 2 }"
          auto-label-width
        >
          <a-form-item field="title" label="工单标题" required>
            <a-input
              v-model="formData.title"
              placeholder="xxx公司-xxx系统-xxx事项"
            />
          </a-form-item>

          <a-form-item field="type" label="工单类型" required>
            <a-select
              v-model="formData.type"
              :style="{ width: '320px' }"
              placeholder="请选择工单类型"
            >
              <a-option value="computer">申请服务器资源</a-option>
              <a-option value="objectStorage">申请对象存储资源</a-option>
              <a-option value="openPort">申请服务器端口开通</a-option>
              <a-option value="account">远程运维人员账号相关</a-option>
              <a-option value="loadBalancing">负载均衡相关</a-option>
              <a-option value="ipaddress">申请地址映射相关流程</a-option>
              <a-option value="domain">申请域名相关流程</a-option>
              <a-option value="scanning">漏洞扫描备案</a-option>
              <a-option value="issue">其他问题处理</a-option>
            </a-select>
          </a-form-item>

          <a-form-item field="assignee" label="指派人" required>
            <a-select
              v-model="formData.assignee"
              :style="{ width: '320px' }"
              placeholder="请选择指派人"
            >
              <a-option
                v-for="assignee in assigneeList"
                :key="assignee.id"
                :value="assignee.username"
                :label="assignee.username"
              />
            </a-select>
          </a-form-item>

          <a-form-item label="相关附件">
            <a-upload
              :file-list="fileList"
              :limit="uploadConfig.maxCount"
              :accept="uploadConfig.accept"
              :max-size="uploadConfig.maxSize"
              :custom-request="handleUpload"
              :on-before-upload="beforeUpload"
              :on-before-remove="handleRemove"
              list-type="text"
              multiple
            >
              <template #upload-button>
                <a-button type="primary">
                  <template #icon>
                    <icon-material-symbols-upload-sharp />
                  </template>
                  点击上传
                </a-button>
              </template>
              <template #extra>
                <div class="upload-tip">
                  支持 jpg、png、pdf、doc、docx、xls、xlsx 格式文件
                  <br />
                  文件大小不超过 3MB，最多可上传 3 个文件
                </div>
              </template>
            </a-upload>
          </a-form-item>

          <a-form-item field="description" label="描述信息">
            <div style="border: 1px solid #ccc">
              <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="editorRef"
                :defaultConfig="toolbarConfig"
                mode="default"
              />
              <Editor
                style="height: 400px; overflow-y: hidden"
                v-model="formData.description"
                :defaultConfig="editorConfig"
                :defaultContent="jsonContent"
                mode="default"
                @onCreated="handleCreated"
              />
            </div>
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="handleSubmit">提交工单</a-button>
          </a-form-item>
        </a-form>
      </template>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.timeline-wrapper {
  display: flex;
  justify-content: center;
  margin: 40px 0;

  :deep(.arco-timeline) {
    width: fit-content;
  }
}

.upload-tip {
  margin-top: 8px;
  color: var(--color-text-3);
  font-size: 12px;
  line-height: 1.5;
}
</style>
