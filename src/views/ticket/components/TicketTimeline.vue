<script lang="ts" setup>
import type { TicketRecord } from '@/types/ticket'

const props = defineProps<{
  ticket: TicketRecord
}>()

// 添加计算属性：获取当前时间线状态
const currentTimelineStatus = computed(() => {
  if (!props.ticket?.ticket_users) {
    return {
      action: '',
      userNames: []
    }
  }

  // 如果工单已完成，返回完成状态
  if (props.ticket.progress === '4') {
    return {
      action: '已完成',
      userNames: [],
      finishTime: props.ticket.finish_time
    }
  }

  if (props.ticket.progress === '5') {
    return {
      action: '已拒绝',
      userNames: [],
      finishTime: props.ticket.finish_time
    }
  }

  if (props.ticket.progress === '6') {
    return {
      action: '已撤销',
      userNames: [],
      finishTime: props.ticket.finish_time
    }
  }

  const ticketUsers = props.ticket.ticket_users
  const pendingApprovers = ticketUsers.filter(
    user => user.type === '审批人' && user.status === '未处理'
  )
  const pendingHandlers = ticketUsers.filter(
    user => user.type === '处理人' && user.status === '未处理'
  )

  // 如果存在未处理的审批人，显示审批中
  if (pendingApprovers.length > 0) {
    return {
      action: '审批中',
      userNames: pendingApprovers.map(user => user.user_name)
    }
  }

  // 如果审批人都已处理，且存在未处理的处理人，显示处理中
  if (pendingHandlers.length > 0) {
    return {
      action: '处理中',
      userNames: pendingHandlers.map(user => user.user_name)
    }
  }

  // 如果都已处理，返回空
  return {
    action: '',
    userNames: []
  }
})
</script>

<template>
  <a-timeline labelPosition="relative">
    <!-- 先展示历史记录 -->
    <a-timeline-item
      v-for="(item, index) in ticket?.timeline"
      :key="index"
      :label="item.time"
    >
      <template #dot>
        <div class="dot-box" v-if="item.decision !== 'rejected'">
          <icon-mdi-check-bold :style="{ color: '#fff' }" />
        </div>
        <div v-else class="dot-box rejected">
          <icon-iconamoon-close-bold :style="{ color: '#fff' }" />
        </div>
      </template>
      <div class="timeline-content">
        <div class="action">{{ item.action }}</div>
        <div class="timeline-meta">
          <span>{{ item.user }}</span>
        </div>
        <div v-if="item.comments" class="comment-block">
          {{ item.comments }}
        </div>
      </div>
    </a-timeline-item>

    <!-- 展示当前进度或完成状态 -->
    <a-timeline-item
      v-if="
        (currentTimelineStatus.action && props.ticket.progress !== '6') ||
        props.ticket.progress === '4' ||
        props.ticket.progress === '5'
      "
      :label="props.ticket.progress === '4' ? props.ticket.finish_time : ''"
    >
      <template #dot>
        <div
          :class="[
            'dot-box',
            props.ticket.progress === '4'
              ? 'completed'
              : props.ticket.progress === '5'
                ? 'rejected'
                : 'current'
          ]"
        >
          <icon-pepicons-pop-clock
            v-if="
              props.ticket.progress !== '4' && props.ticket.progress !== '5'
            "
            :style="{ color: '#fff' }"
          />
          <icon-mdi-check-bold v-else :style="{ color: '#fff' }" />
        </div>
      </template>
      <div
        class="timeline-content"
        :class="{
          completed: props.ticket.progress === '4',
          rejected: props.ticket.progress === '5'
        }"
      >
        <div class="action">
          {{ currentTimelineStatus.action }}
          <template
            v-if="
              props.ticket.progress !== '4' && props.ticket.progress !== '5'
            "
            >...</template
          >
        </div>
        <div v-if="props.ticket.progress !== '4'" class="timeline-meta">
          <template v-if="currentTimelineStatus.action === '审批中'">
            需要以下审批人同意
          </template>
          <template v-else-if="currentTimelineStatus.action === '处理中'">
            需要以下处理人处理
          </template>
        </div>
        <div
          v-if="currentTimelineStatus.userNames.length > 0"
          class="approver-list"
        >
          <a-space>
            <a-tag
              v-for="userName in currentTimelineStatus.userNames"
              :key="userName"
            >
              {{ userName }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-timeline-item>
  </a-timeline>
</template>

<style lang="scss" scoped>
.timeline-content {
  .action {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .timeline-meta {
    font-size: 13px;
    color: var(--color-text-3);
    margin-bottom: 8px;
  }

  .comment-block {
    background-color: var(--color-fill-2);
    padding: 12px;
    border-radius: 4px;
    font-size: 13.5px;
    color: var(--color-text-1);
    line-height: 1.3;
    white-space: pre-wrap;
    word-break: break-all;
  }

  &.completed {
    .action {
      color: rgb(var(--green-6));
    }
  }
}

.dot-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15px;
  height: 15px;
  font-size: 12px;
  padding: 2px;
  box-sizing: border-box;
  border-radius: 50%;
  background-color: rgb(var(--green-6));

  &.rejected {
    background-color: rgb(var(--red-6));
  }

  &.current {
    background-color: rgb(var(--arcoblue-6));
  }

  &.completed {
    background-color: rgb(var(--green-6));
  }
}
</style>
