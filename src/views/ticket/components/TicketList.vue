<script lang="ts" setup>
import type { TicketRecord, Attachment } from '@/types/ticket'
import { useUserStore } from '@/stores'
import { ref, computed, watch } from 'vue'
import type { SelectOptionData } from '@arco-design/web-vue'
import { reqUpdateTicketUsers, reqUpdateTicket } from '@/api/ticket'
import { reqGetUserList } from '@/api/user'
import type { UserRecord } from '@/types/user'
import { ticketTypeConfig } from '@/types/ticket'
import TicketTimeline from './TicketTimeline.vue'
import { reqDownloadAttachment } from '@/api/ticket'

// Props 定义
const props = withDefaults(
  defineProps<{
    tickets: TicketRecord[]
  }>(),
  {
    tickets: () => []
  }
)

// Emits 定义
const emit = defineEmits<{
  (e: 'refresh'): void
}>()

// Store 实例
const userStore = useUserStore()

// ================ 工单列表相关 ================

const selectedTicket = ref<TicketRecord | null>(null)

// 监听工单列表变化,更新选中工单信息
watch(
  () => props.tickets,
  newTickets => {
    if (selectedTicket.value?.id) {
      const updatedTicket = newTickets.find(
        ticket => ticket.id === selectedTicket.value?.id
      )
      if (updatedTicket) {
        selectedTicket.value = updatedTicket
      }
    }
  },
  { deep: true }
)

// 工单状态配置
const statusConfig = {
  '1': {
    color: '#165DFF',
    bgColor: '#E8F3FF',
    text: '待处理'
  },
  '2': {
    color: '#FF7D00',
    bgColor: '#FFF3E8',
    text: '审批中'
  },
  '3': {
    color: '#FF7D00',
    bgColor: '#FFF3E8',
    text: '处理中'
  },
  '4': {
    color: '#00B42A',
    bgColor: '#E8FFEA',
    text: '已完成'
  },
  '5': {
    color: '#F53F3F',
    bgColor: '#FFE8E8',
    text: '已拒绝'
  },
  '6': {
    color: '#86909C',
    bgColor: '#F2F3F5',
    text: '已撤销'
  }
}

// 获取工单状态文本
const getStatusText = (progress: string) => {
  return statusConfig[progress as keyof typeof statusConfig]?.text || '未知状态'
}

// 获取工单状态样式
const getStatusStyle = (progress: string) => {
  const status = statusConfig[progress as keyof typeof statusConfig]
  return {
    backgroundColor: status?.bgColor || '',
    color: status?.color || ''
  }
}

// 获取工单类型文本
const getTicketTypeText = (type: string) => {
  return ticketTypeConfig[type as keyof typeof ticketTypeConfig]?.label || type
}

// ================ 搜索和筛选相关 ================

// 搜索关键字
const searchKeyword = ref('')

// 筛选表单数据
const filterVisible = ref(false)
const filterForm = ref({
  submitter: '',
  progress: '',
  createTimeRange: [] as string[],
  finishTimeRange: [] as string[]
})

// 工单进度选项
const progressOptions: SelectOptionData[] = [
  { label: '审批中', value: '2' },
  { label: '办理中', value: '3' },
  { label: '已完成', value: '4' },
  { label: '已拒绝', value: '5' },
  { label: '已撤销', value: '6' }
]

// 过滤后的工单列表
const filteredTickets = computed(() => {
  let result = [...props.tickets]

  // 标题关键字过滤
  if (searchKeyword.value) {
    result = result.filter(ticket =>
      ticket.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 发起人模糊匹配
  if (filterForm.value.submitter) {
    const keyword = filterForm.value.submitter.toLowerCase()
    result = result.filter(ticket =>
      ticket.submitter.toLowerCase().includes(keyword)
    )
  }

  // 进度筛选
  if (filterForm.value.progress) {
    result = result.filter(
      ticket => ticket.progress === filterForm.value.progress
    )
  }

  // 创建时间区间筛选
  if (filterForm.value.createTimeRange?.length === 2) {
    const startTime = new Date(filterForm.value.createTimeRange[0]).getTime()
    const endTime = new Date(filterForm.value.createTimeRange[1]).getTime()

    result = result.filter(ticket => {
      const ticketTime = new Date(ticket.create_time as string).getTime()
      return ticketTime >= startTime && ticketTime <= endTime
    })
  }

  // 完成时间区间筛选
  if (filterForm.value.finishTimeRange?.length === 2) {
    const startTime = new Date(filterForm.value.finishTimeRange[0]).getTime()
    const endTime = new Date(filterForm.value.finishTimeRange[1]).getTime()

    result = result.filter(ticket => {
      if (!ticket.finish_time) return false
      const ticketTime = new Date(ticket.finish_time).getTime()
      return ticketTime >= startTime && ticketTime <= endTime
    })
  }

  return result
})

// ================ 权限判断相关 ================

// 判断当前用户是否有审批权限
const canApprove = computed(() => {
  if (!selectedTicket.value?.ticket_users) return false
  return selectedTicket.value.ticket_users.some(
    user =>
      user.type === '审批人' &&
      user.status === '未处理' &&
      user.user_name === userStore.userInfo.username
  )
})

// 判断当前用户是否为处理人
const isHandler = computed(() => {
  if (!selectedTicket.value) return false
  return (
    selectedTicket.value.assignee === userStore.userInfo.username &&
    !['4', '5', '6'].includes(selectedTicket.value.progress)
  )
})

// 添加撤销权限判断
const canRevoke = computed(() => {
  if (!selectedTicket.value) return false
  // 只有工单提交人可以撤销，且工单状态不能是已完成、已拒绝或已撤销
  return (
    selectedTicket.value.submitter === userStore.userInfo.username &&
    !['4', '5', '6'].includes(selectedTicket.value.progress)
  )
})

// 添加完成办理权限判断
const canFinish = computed(() => {
  if (!selectedTicket.value?.ticket_users) return false

  const ticketUsers = selectedTicket.value.ticket_users
  const currentUser = userStore.userInfo.username

  // 检查工单是否已拒绝或已撤销
  if (['5', '6'].includes(selectedTicket.value.progress)) return false

  // 检查当前用户是否是未处理的处理人
  const isUnhandledHandler = ticketUsers.some(
    user =>
      user.type === '处理人' &&
      user.status === '未处理' &&
      user.user_name === currentUser
  )

  // 检查是否所有审批人都已处理
  const allApproversHandled = ticketUsers
    .filter(user => user.type === '审批人')
    .every(user => user.status === '已处理')

  return isUnhandledHandler && allApproversHandled
})

// ================ 操作相关 ================

// 弹窗控制
const approveVisible = ref(false)
const rejectVisible = ref(false)
const commentVisible = ref(false)
const revokeVisible = ref(false)
const assignVisible = ref(false)
const finishVisible = ref(false)

// 修改表单数据定义
const formData = ref({
  approveComments: '', // 审批意见
  rejectComments: '', // 拒绝意见
  commentContent: '', // 备注内容
  revokeComments: '', // 撤销原因
  finishComments: '' // 完成办理意见
})

// 表单校验规则
const rules = {
  comments: [{ required: true, message: '请输入意见内容' }]
}

// 修改处理按钮点击函数
const handleApprove = () => {
  formData.value.approveComments = ''
  approveVisible.value = true
}

const handleReject = () => {
  formData.value.rejectComments = ''
  rejectVisible.value = true
}

const handleComment = () => {
  formData.value.commentContent = ''
  commentVisible.value = true
}

const handleRevoke = () => {
  formData.value.revokeComments = ''
  revokeVisible.value = true
}

// 修改提交函数
const handleSubmitComment = async () => {
  if (!selectedTicket.value?.id) return
  if (!formData.value.commentContent) {
    AMessage.error('请输入备注内容')
    return
  }

  try {
    await reqUpdateTicket({
      id: selectedTicket.value.id,
      type: 'comment',
      comments: formData.value.commentContent,
      username: userStore.userInfo.username
    })

    AMessage.success('备注成功')
    commentVisible.value = false
  } catch (error: any) {
    console.error('备注失败:', error)
    AMessage.error(error.message || '备注失败')
  } finally {
    emit('refresh')
  }
}

const handleSubmitApprove = async () => {
  if (!selectedTicket.value?.id) return
  try {
    await reqUpdateTicket({
      id: selectedTicket.value.id,
      type: 'approval',
      comments: formData.value.approveComments,
      decision: 'approved',
      username: userStore.userInfo.username
    })
    AMessage.success('审批成功')
    approveVisible.value = false
  } catch (error) {
    AMessage.error('审批失败')
  } finally {
    emit('refresh')
  }
}

const handleSubmitRevoke = async () => {
  if (!selectedTicket.value?.id) return
  try {
    await reqUpdateTicket({
      id: selectedTicket.value.id,
      type: 'revoke',
      comments: formData.value.revokeComments,
      username: userStore.userInfo.username
    })
    AMessage.success('撤销成功')
    revokeVisible.value = false
  } catch (error) {
    AMessage.error('撤销失败')
  } finally {
    emit('refresh')
  }
}

const handleSubmitReject = async () => {
  if (!selectedTicket.value?.id) return
  try {
    await reqUpdateTicket({
      id: selectedTicket.value.id,
      type: 'approval',
      comments: formData.value.rejectComments,
      decision: 'rejected',
      username: userStore.userInfo.username
    })
    AMessage.success('已拒绝')
    rejectVisible.value = false
  } catch (error) {
    AMessage.error('拒绝失败')
  } finally {
    emit('refresh')
    selectedTicket.value = null
  }
}

const handleFinish = () => {
  formData.value.finishComments = ''
  // 显示完成办理弹窗
  finishVisible.value = true
}

const handleSubmitFinish = async () => {
  if (!selectedTicket.value?.id) return
  try {
    await reqUpdateTicket({
      id: selectedTicket.value.id,
      type: 'handle',
      comments: formData.value.finishComments,
      username: userStore.userInfo.username
    })
    AMessage.success('办理完成')
    finishVisible.value = false
  } catch (error) {
    AMessage.error('操作失败')
  } finally {
    emit('refresh')
    selectedTicket.value = null
  }
}

// ================ 人员分配相关 ================

const userListData = ref<UserRecord[]>([])

// 获取用户列表
const getUserList = async () => {
  const res = await reqGetUserList({
    page: 1,
    page_size: 1000
  })
  userListData.value = res.data.users
}

// 分配表单数据
const assignForm = ref({
  approvers: [] as string[],
  handlers: [] as string[]
})

// 用户选项列表
const filteredUsers = computed(() => {
  return userListData.value.map(user => ({
    label: `${user.auth_mode === 'local' ? '本地认证' : 'LDAP'}-(${user.username})`,
    value: user.username,
    disabled: selectedTicket.value?.assignee === user.username
  }))
})

// 处理分配人员
const handleAssign = async () => {
  if (!selectedTicket.value?.id) {
    AMessage.error('请先选择工单')
    return
  }

  const users = [
    ...assignForm.value.approvers.map(name => ({
      user_name: name,
      type: '审批人' as const,
      status: '未处理' as const
    })),
    ...assignForm.value.handlers.map(name => ({
      user_name: name,
      type: '处理人' as const,
      status: '未处理' as const
    }))
  ]

  try {
    await reqUpdateTicketUsers({
      id: selectedTicket.value.id,
      users
    })
    AMessage.success('分配人员成功')
    assignVisible.value = false
    // 触发父组件刷新
    emit('refresh')
  } catch (error) {
    AMessage.error('分配人员失败')
  }
}

// 打开分配弹窗
const openAssignModal = async () => {
  if (!selectedTicket.value) {
    AMessage.error('请先选择工单')
    return
  }

  // 获取用户列表
  await getUserList()

  // 从当前工单的 ticket_users 中提取审批人和处理人
  if (selectedTicket.value.ticket_users?.length) {
    const approvers = selectedTicket.value.ticket_users
      .filter(user => user.type === '审批人')
      .map(user => user.user_name)

    const handlers = selectedTicket.value.ticket_users
      .filter(user => user.type === '处理人')
      .map(user => user.user_name)

    // 设置表单默认值
    assignForm.value = {
      approvers,
      handlers
    }
  } else {
    // 如果没有现有处理人，则重置表单
    resetAssignForm()
  }

  assignVisible.value = true
}

// 修改重置表单函数，使其返回一个空的默认值对象
const getDefaultAssignForm = () => ({
  approvers: [] as string[],
  handlers: [] as string[]
})

const resetAssignForm = () => {
  assignForm.value = getDefaultAssignForm()
}

// 添加计算属性：获取当前时间线状态
const currentTimelineStatus = computed(() => {
  if (!selectedTicket.value?.ticket_users) {
    return {
      action: '',
      userNames: []
    }
  }

  const ticketUsers = selectedTicket.value.ticket_users
  const pendingApprovers = ticketUsers.filter(
    user => user.type === '审批人' && user.status === '未处理'
  )
  const pendingHandlers = ticketUsers.filter(
    user => user.type === '处理人' && user.status === '未处理'
  )

  // 如果存在未处理的审批人，显示审批中
  if (pendingApprovers.length > 0) {
    return {
      action: '审批中',
      userNames: pendingApprovers.map(user => user.user_name)
    }
  }

  // 如果审批人都已处理，且存在未处理的处理人，显示处理中
  if (pendingHandlers.length > 0) {
    return {
      action: '处理中',
      userNames: pendingHandlers.map(user => user.user_name)
    }
  }

  // 如果都已处理，返回空
  return {
    action: '',
    userNames: []
  }
})

// ================ 基础操作函数 ================

// 点击列表项
const handleTicketClick = (record: TicketRecord) => {
  selectedTicket.value = record
}

// 重置筛选条件
const handleReset = () => {
  filterForm.value = {
    submitter: '',
    progress: '',
    createTimeRange: [],
    finishTimeRange: []
  }
}

// 处理筛选
const handleFilter = () => {
  filterVisible.value = false
}

// 搜索
const handleSearch = (value: string) => {
  searchKeyword.value = value
}

// 刷新 - 重置所有筛选条件
const handleRefresh = () => {
  searchKeyword.value = ''
  handleReset()
}

// 添加加载状态
const downloadingFiles = ref<Set<string>>(new Set())

// 修改下载附件的处理函数
const handleDownloadAttachment = async (file: Attachment) => {
  // 如果正在下载，则不处理
  if (downloadingFiles.value.has(file.file_name)) {
    return
  }

  try {
    // 添加到下载集合
    downloadingFiles.value.add(file.file_name)

    const response = await reqDownloadAttachment(file.src, file.file_name)

    const urlObject = window.URL || window.webkitURL
    const blob = new Blob([response.data], { type: 'application/octet-stream' })
    const downloadUrl = urlObject.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = file.file_name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(downloadUrl)
    AMessage.success('下载成功')
  } catch (error: any) {
    AMessage.error('下载文件失败')
  } finally {
    // 从下载集合中移除
    downloadingFiles.value.delete(file.file_name)
  }
}
</script>

<template>
  <div class="ticket-list-container">
    <div class="ticket-list-content">
      <!-- 搜索区域 -->
      <div class="search-box">
        <a-input-search
          v-model="searchKeyword"
          placeholder="搜索工单标题"
          allow-clear
          :style="{ width: '100%', flex: '1 1 0%' }"
          @search="handleSearch"
        />

        <a-button @click="handleRefresh" class="btn">
          <template #icon>
            <icon-material-symbols-refresh-rounded />
          </template>
        </a-button>

        <a-popover
          trigger="click"
          position="rt"
          v-model:popup-visible="filterVisible"
        >
          <a-button class="btn">
            <template #icon>
              <icon-mdi-filter-outline />
            </template>
          </a-button>
          <template #content>
            <div class="filter-popover">
              <a-form :model="filterForm" layout="vertical">
                <a-form-item field="submitter">
                  <a-input
                    v-model="filterForm.submitter"
                    placeholder="请输入发起人"
                    allow-clear
                  />
                </a-form-item>
                <a-form-item field="progress">
                  <a-select
                    v-model="filterForm.progress"
                    placeholder="请选择进度"
                    allow-clear
                    :options="progressOptions"
                  />
                </a-form-item>
                <a-form-item field="createTimeRange">
                  <a-range-picker
                    v-model="filterForm.createTimeRange"
                    style="width: 100%"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </a-form-item>
                <a-form-item field="finishTimeRange">
                  <a-range-picker
                    v-model="filterForm.finishTimeRange"
                    style="width: 100%"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </a-form-item>
                <div class="filter-actions">
                  <a-space>
                    <a-button @click="handleReset">重置</a-button>
                    <a-button type="primary" @click="handleFilter">
                      确定
                    </a-button>
                  </a-space>
                </div>
              </a-form>
            </div>
          </template>
        </a-popover>
      </div>
      <div class="flow-list-box">
        <div
          class="item-box"
          :class="{ active: selectedTicket?.id === item.id }"
          v-for="(item, index) in filteredTickets"
          :key="index"
          @click="handleTicketClick(item)"
        >
          <div class="header">
            <a-typography-paragraph
              class="title"
              :ellipsis="{
                rows: 1,
                showTooltip: true
              }"
            >
              <b>{{ item.title }}</b>
            </a-typography-paragraph>

            <a-tag :style="getStatusStyle(item.progress)">
              {{ getStatusText(item.progress) }}
            </a-tag>
          </div>
          <div class="footer">
            <span class="username">{{ item.submitter }}</span>
            <span class="time" style="color: var(--color-neutral-6)">
              提交于 {{ item.create_time }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="ticket-detail-content" v-if="selectedTicket">
      <div class="flow-header-box">
        <span class="flow-id"> 编号: {{ selectedTicket?.id }} </span>
      </div>
      <div class="action-area-box">
        <div class="detail-header">
          <div class="title-row">
            <h2 class="title">{{ selectedTicket?.title }}</h2>
            <a-tag :style="getStatusStyle(selectedTicket?.progress || '')">
              {{ getStatusText(selectedTicket?.progress || '') }}
            </a-tag>
          </div>
          <div class="meta-info">
            <a-space>
              <span class="submitter">{{ selectedTicket?.submitter }}</span>
              <span class="time">{{ selectedTicket?.create_time }} 提交</span>
            </a-space>
          </div>
        </div>
        <a-divider />
        <div class="detail-content">
          <div class="section">
            <a-descriptions :column="1">
              <a-descriptions-item label="工单类型">
                {{ getTicketTypeText(selectedTicket?.type || '') }}
              </a-descriptions-item>
              <a-descriptions-item label="处理人">{{
                selectedTicket?.assignee
              }}</a-descriptions-item>
              <a-descriptions-item label="工单描述">
                <div v-html="selectedTicket?.description"></div>
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <div class="section" v-if="selectedTicket?.attachments?.length">
            <h4 class="section-title">附件</h4>
            <div class="attachments">
              <a-space>
                <a-tag
                  v-for="file in selectedTicket?.attachments"
                  :key="file.file_name"
                  class="attachment-tag"
                  hoverable
                  :loading="downloadingFiles.has(file.file_name)"
                  @click="handleDownloadAttachment(file)"
                >
                  <a-space>
                    <icon-akar-icons-file />
                    {{ file.file_name }}
                  </a-space>
                </a-tag>
              </a-space>
            </div>
          </div>
        </div>
        <a-divider />
        <div class="section">
          <h3 class="section-title">处理进度</h3>
          <TicketTimeline :ticket="selectedTicket" />
        </div>
      </div>
      <div class="flow-actions-box">
        <a-space>
          <!-- 评论按钮始终显示 -->
          <a-button type="secondary" @click="handleComment">
            <template #icon>
              <icon-iconamoon-comment class="btn" />
            </template>
            <template #default>备注</template>
          </a-button>

          <!-- 分配人员按钮只在当前用户是处理人时显示 -->
          <a-button v-if="isHandler" type="secondary" @click="openAssignModal">
            <template #icon>
              <icon-mingcute-add-fill class="btn" />
            </template>
            <template #default>分配人员</template>
          </a-button>

          <!-- 同意和拒绝按钮只在当前用户可以审批时显示 -->
          <template
            v-if="canApprove && !['5', '6'].includes(selectedTicket?.progress)"
          >
            <a-button type="primary" @click="handleApprove">
              <template #icon>
                <icon-ic-outline-done class="btn" />
              </template>
              <template #default>同意</template>
            </a-button>

            <a-button type="primary" status="danger" @click="handleReject">
              <template #icon>
                <icon-material-symbols-close-rounded class="btn" />
              </template>
              <template #default>拒绝</template>
            </a-button>
          </template>

          <!-- 完成办理按钮 -->
          <a-button
            v-if="canFinish"
            type="primary"
            status="success"
            @click="handleFinish"
          >
            <template #icon>
              <icon-ic-outline-download-done class="btn" />
            </template>
            <template #default>完成办理</template>
          </a-button>

          <!-- 撤销按钮只在符合条件时显示 -->
          <a-button
            v-if="canRevoke"
            type="primary"
            status="danger"
            @click="handleRevoke"
          >
            <template #icon>
              <icon-material-symbols-close-rounded class="btn" />
            </template>
            <template #default>撤销</template>
          </a-button>
        </a-space>
      </div>
    </div>
    <div class="ticket-detail-content empty" v-else>
      <a-empty description="请选择工单查看详情" />
    </div>
  </div>

  <!-- 同意审批弹窗 -->
  <a-modal
    v-model:visible="approveVisible"
    title="同意审批"
    @ok="handleSubmitApprove"
    @cancel="approveVisible = false"
  >
    <a-form :model="formData">
      <a-form-item field="approveComments" label="审批意见">
        <a-textarea
          v-model="formData.approveComments"
          placeholder="请输入审批意见（选填）"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 拒绝审批弹窗 -->
  <a-modal
    v-model:visible="rejectVisible"
    title="拒绝审批"
    @ok="handleSubmitReject"
    @cancel="rejectVisible = false"
  >
    <a-form :model="formData">
      <a-form-item field="rejectComments" label="审批意见">
        <a-textarea
          v-model="formData.rejectComments"
          placeholder="请输入审批意见（选填）"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 备注弹窗 -->
  <a-modal
    v-model:visible="commentVisible"
    title="添加备注"
    @ok="handleSubmitComment"
    @cancel="commentVisible = false"
  >
    <a-form :model="formData" :rules="rules">
      <a-form-item field="commentContent" label="备注内容">
        <a-textarea
          v-model="formData.commentContent"
          placeholder="请输入备注内容"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 分配人员弹窗 -->
  <a-modal
    v-model:visible="assignVisible"
    title="分配人员"
    @cancel="resetAssignForm"
    @before-ok="handleAssign"
  >
    <a-form :model="assignForm" layout="vertical">
      <a-form-item field="approvers" label="审批人">
        <a-select
          v-model="assignForm.approvers"
          placeholder="请选择审批人"
          multiple
          allow-clear
          allow-search
          :options="filteredUsers"
        />
      </a-form-item>
      <a-form-item field="handlers" label="处理人">
        <a-select
          v-model="assignForm.handlers"
          placeholder="请选择处理人"
          multiple
          allow-clear
          allow-search
          :options="filteredUsers"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 撤销弹窗 -->
  <a-modal
    v-model:visible="revokeVisible"
    title="撤销工单"
    @ok="handleSubmitRevoke"
    @cancel="revokeVisible = false"
  >
    <a-form :model="formData">
      <a-form-item field="revokeComments" label="撤销原因">
        <a-textarea
          v-model="formData.revokeComments"
          placeholder="请输入撤销原因"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 完成办理弹窗 -->
  <a-modal
    v-model:visible="finishVisible"
    title="完成办理"
    @ok="handleSubmitFinish"
    @cancel="finishVisible = false"
  >
    <a-form :model="formData">
      <a-form-item field="finishComments" label="办理意见">
        <a-textarea
          v-model="formData.finishComments"
          placeholder="请输入办理意见（选填）"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style lang="scss">
.ticket-list-container {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 15px;
}

.btn {
  display: flex;
  align-items: center;
  margin-left: 4px;
}

.ticket-list-content {
  width: 330px;
  min-width: 330px;
  height: 100%;
  background-color: var(--color-bg-2);
  border-radius: 5px;
  box-sizing: border-box;

  .search-box {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 12px;
  }

  .flow-list-box {
    padding: 0 12px;
    height: calc(100% - 56px);
    overflow: auto;

    .item-box {
      height: 60px;
      margin-bottom: 12px;
      user-select: none;
      border-radius: 6px;
      padding: 10px 12px;
      overflow: hidden;
      border: 1.5px solid #e9ebef;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        border-color: rgb(var(--arcoblue-5));
      }

      .header {
        display: flex;
        height: 25px;
        align-items: center;
        justify-content: space-between;
        gap: 8px;

        .title {
          flex: 1;
          margin: 0;
          align-items: center;
          font-weight: 500;
          text-align: left;
          min-width: 0;
        }

        .arco-tag {
          flex-shrink: 0;
        }
      }

      .footer {
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        height: 30px;
        font-weight: 500;
      }
    }
  }
}

.ticket-detail-content {
  height: 100%;
  overflow: hidden;
  background: var(--color-bg-2);
  border-radius: 6px;
  flex: auto;
  display: flex;
  flex-direction: column;

  &.empty {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flow-header-box {
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--color-border-2);

    .flow-id {
      font-size: 12px;
      font-weight: 500;
      color: var(--color-neutral-6);
    }
  }

  .action-area-box {
    flex: 1;
    overflow: auto;
    padding: 0 30px;

    .detail-header {
      .title-row {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
      }
    }
  }

  .flow-actions-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 52px;
    border-top: 1px solid var(--color-neutral-3);
    padding: 0 20px;
  }
}

.filter-popover {
  width: 250px;

  .filter-actions {
    display: flex;
    justify-content: flex-end;
  }
}

.current-timeline {
  margin: 16px 0;
  padding: 12px;
  background-color: var(--color-fill-2);
  border-radius: 4px;

  .status {
    margin-bottom: 8px;
  }

  .users {
    color: var(--color-text-2);
    font-size: 14px;
  }
}

.timeline-content {
  .action {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .timeline-meta {
    font-size: 13px;
    color: var(--color-text-3);
    margin-bottom: 8px;
  }

  .comment-block {
    background-color: var(--color-fill-2);
    padding: 12px;
    border-radius: 4px;
    font-size: 13.5px;
    color: var(--color-text-1);
    line-height: 1.3;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.dot-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15px;
  height: 15px;
  font-size: 12px;
  padding: 2px;
  box-sizing: border-box;
  border-radius: 50%;
  background-color: rgb(var(--green-6));

  &.rejected {
    background-color: rgb(var(--red-6));
  }

  &.current {
    background-color: rgb(var(--arcoblue-6));
  }
}

.attachment-tag {
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: var(--color-fill-3);
  }

  &[loading] {
    cursor: not-allowed;
    opacity: 0.7;
  }
}
</style>
