<script lang="ts" setup>
import TicketList from '@/views/ticket/components/TicketList.vue'
import { ref } from 'vue'
import { reqGetTicketList } from '@/api/ticket'
import type { TicketRecord } from '@/types/ticket'
import { useUserStore } from '@/stores'
const userStore = useUserStore()

// 当前选中的标签页
const activeTab = ref('my_submit')

// 工单列表数据
const tickets = ref<TicketRecord[]>([])

// 获取工单列表数据
const fetchTickets = async (viewType: string) => {
  try {
    const { data } = await reqGetTicketList({
      page_size: 999,
      page: 1,
      assignee: userStore.userInfo.username,
      submitter: userStore.userInfo.username,
      // assignee: useUserStore.userInfo.username,
      view_type: viewType
    })
    tickets.value = data.tickets
  } catch (error) {
    // 可以添加错误处理
    console.error('获取工单列表失败:', error)
  }
}

// 处理标签页变化
const handleTabChange = (key: string | number) => {
  // console.log('key', key)
  fetchTickets(key as string)
}

// 初始加载
fetchTickets('my_submit')
</script>

<template>
  <div class="container">
    <div class="tab-wrapper">
      <a-tabs
        v-model:activeKey="activeTab"
        type="rounded"
        size="small"
        hide-content
        @change="handleTabChange"
      >
        <a-tab-pane key="my_submit">
          <template #title>
            <icon-ix-add-application class="icon" />
            <span>我的申请</span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="pending">
          <template #title>
            <icon-fluent-mdl2-waitlist-confirm class="icon" />
            <span>待处理</span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="processed">
          <template #title>
            <icon-lets-icons-done-all-round class="icon" />
            <span>已处理</span>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="ticket-list-wrapper">
      <TicketList
        :tickets="tickets"
        :key="activeTab"
        @refresh="fetchTickets(activeTab)"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 58px - 30px);
  overflow: hidden;
}

.tab-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45px;
  background-color: var(--color-bg-2);
  border-radius: 5px;
  flex-shrink: 0;

  .icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.ticket-list-wrapper {
  flex: 1;
  margin-top: 20px;
  overflow-y: auto;
  min-height: 0;
}
</style>
