<script setup lang="ts">
import type { TableColumnData, FieldRule } from '@arco-design/web-vue'
import type { HostsListParams, HostsRecord } from '@/types/hosts'
import {
  formatBytes,
  formatSpeed,
  secondsToDays,
  formatGigaBytes
} from '@/utils/format'
import { reqGetGroupList } from '@/api/group'
import type { GroupRecord } from '@/types/group'
import { cloneDeep } from 'lodash'
import Sortable from 'sortablejs'
import {
  reqGetHostsList,
  reqAddHosts,
  reqUpdateHosts,
  reqDeleteHosts,
  reqGetOsGroups,
  reqGetLocationGroups
} from '@/api/hosts'
import { useUserStore } from '@/stores'

const userStore = useUserStore()
const ws = ref<WebSocket | null>(null)

// 搜索表单相关
const searchFormRef = ref()
const searchForm = reactive<HostsListParams>({
  page: 1,
  page_size: 10
})

// 表格数据相关
const tableData = ref<HostsRecord[]>([])
const loading = ref(false)
const total = ref(0)
// 分页配置
const pagination = computed(() => ({
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  pageSizeOptions: [10, 20, 30, 50], // 每页条数选项
  total: total.value, // 总条数
  current: searchForm.page, // 当前页码
  pageSize: searchForm.page_size // 每页条数
}))

// 表格列配置
const columns = reactive<TableColumnData[]>([
  {
    dataIndex: 'hostname',
    title: '主机名称',
    width: 240,
    slotName: 'hostname'
  },
  {
    dataIndex: 'ip',
    title: 'IP地址',
    width: 140,
    render: ({ record }) => record.ip.join(', ')
  },
  {
    dataIndex: 'os',
    title: '操作系统',
    width: 120,
    slotName: 'os'
  },
  {
    dataIndex: 'groups',
    title: '主机组',
    width: 120,
    ellipsis: true,
    tooltip: true,
    render: ({ record }) => getGroupNames(record.groups)
  },
  {
    dataIndex: 'boot_time',
    title: '启动时间',
    width: 110,
    slotName: 'bootTime',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'cpu_total',
    title: 'CPU',
    width: 110,
    slotName: 'cpu_total',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'memory_total',
    title: 'MEM',
    width: 110,
    slotName: 'memory_total',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'disk_total',
    title: 'DISK',
    width: 110,
    slotName: 'disk_total',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'cpu_usage',
    title: 'CPU 使用率',
    width: 140,
    slotName: 'cpu_usage',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'memory_usage',
    title: 'MEM 使用率',
    width: 140,
    slotName: 'memory_usage',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'disk_root_usage',
    title: '/ 使用率',
    width: 140,
    slotName: 'disk_root_usage',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'net_in_speed',
    title: '下载',
    width: 120,
    slotName: 'netIn',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'net_out_speed',
    title: '上传',
    width: 120,
    slotName: 'netOut',
    sortable: {
      sorter: true,
      sortDirections: ['descend']
    }
  },
  {
    dataIndex: 'action',
    title: '操作',
    slotName: 'action',
    fixed: 'right',
    width: 150
  }
])

// 定义列的类型，继承自TableColumnData并添加checked属性用于控制列的显示/隐藏
type Column = TableColumnData & { checked: boolean }

// 用于存储所有列的配置信息，包括显示状态和顺序
const columnSettings = ref<Column[]>([])

// 初始化列设置，将原始列配置转换为带有checked属性的配置
const initColumnSettings = () => {
  columnSettings.value = columns.map(col => ({
    ...cloneDeep(col), // 深拷贝防止互相影响
    checked: true // 默认所有列都显示
  }))
}

// 计算当前应该显示的列，过滤出checked为true的列
const visibleColumns = computed(() => {
  return columnSettings.value.filter(col => col.checked)
})

// 处理列的显示/隐藏状态改变
const handleColumnChange = (
  value: boolean | (string | number | boolean)[], // checkbox的值
  ev: Event, // 事件对象
  column: Column // 当前列配置
) => {
  const targetColumn = columnSettings.value.find(
    col => col.dataIndex === column.dataIndex
  )
  if (targetColumn) {
    targetColumn.checked = Boolean(value) // 确保转换为布尔值
  }
}

// 处理列的拖拽排序
const handleColumnSort = (evt: any) => {
  const { oldIndex, newIndex } = evt
  if (oldIndex !== newIndex) {
    // 使用数组方法重新排序
    const newColumns = [...columnSettings.value]
    const moveItem = newColumns.splice(oldIndex, 1)[0] // 移除拖拽的项
    newColumns.splice(newIndex, 0, moveItem) // 插入到新位置
    columnSettings.value = newColumns // 更新列设置
  }
}

// 初始化拖拽排序功能
const initSortable = () => {
  nextTick(() => {
    const el = document.getElementById('tableSetting')
    if (el) {
      // 创建Sortable实例，启用拖拽功能
      new Sortable(el, {
        animation: 150, // 拖拽时的动画效果
        onEnd: handleColumnSort // 拖拽结束时的处理函数
      })
    }
  })
}

// 列设置弹窗显示状态改变的处理
const popupVisibleChange = (visible: boolean) => {
  if (visible) {
    // 弹窗显示时初始化拖拽功能
    initSortable()
  }
}

// 在组件挂载时初始化列设置
onMounted(() => {
  initColumnSettings()
})

// 表格排序回调
const onSortChange = (dataIndex: string, direction: string) => {
  if (!direction) {
    delete searchForm.sort_by
    delete searchForm.sort_order
  } else {
    if (
      [
        'cpu_usage',
        'memory_usage',
        'disk_root_usage',
        'net_in_speed',
        'net_out_speed',
        'boot_time'
      ].includes(dataIndex)
    ) {
      searchForm.sort_by = `metrics.${dataIndex}`
    } else {
      searchForm.sort_by = dataIndex
    }
    searchForm.sort_order = direction
  }
  sendQuery()
}

// 主机表单相关
const visible = ref(false) // 模态框显示状态
const hostFormRef = ref() // 表单引用
const hostFormData = ref<HostsRecord>({
  // 表单数据
  hostname: '',
  hardware_type: 'virtual',
  ip: [],
  os: '',
  cpu_total: undefined,
  memory_total: undefined,
  disk_total: undefined,
  location: '',
  groups: [],
  status: 'offline',
  description: ''
})

// 表单验证规则
const formRules: Record<string, FieldRule | FieldRule[]> = {
  hardware_type: [{ required: true, message: '请选择硬件类型' }],
  hostname: [
    { required: true, message: '请输入主机名称' },
    {
      match: /^[a-zA-Z0-9.-]+$/,
      message: '主机名只能包含字母、数字、点和横线'
    }
  ],
  ip: [
    { required: true, message: '请输入IP地址' },
    {
      validator: (value, callback) => {
        if (!value || value.length === 0) {
          callback('请输入IP地址')
          return
        }
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
        const isValid = value.every((ip: string) => ipRegex.test(ip))
        if (!isValid) {
          callback('请输入有效的IP地址')
          return
        }
        callback()
      }
    }
  ],
  os: [{ required: true, message: '请选择操作系统' }],
  cpu_total: [
    { required: true, message: '请输入CPU核心数' },
    {
      validator: (value, callback) => {
        if (value < 1) {
          callback('CPU核心数必须大于0')
          return
        }
        callback()
      }
    }
  ],
  memory_total: [
    { required: true, message: '请输入内存大小' },
    {
      validator: (value, callback) => {
        if (value < 1) {
          callback('内存大小必须大于0')
          return
        }
        callback()
      }
    }
  ],
  disk_total: [{ required: true, message: '请输入磁盘大小' }],
  location: [{ required: true, message: '请输入位置信息' }]
}

// 主机组相关
const groupListOptions = ref<GroupRecord[]>([])

// 获取主机组列表
const getGroupList = async () => {
  try {
    const res = await reqGetGroupList({ page: 1, page_size: 999 })
    groupListOptions.value = res.data.groups
  } catch (error: any) {
    console.error('获取组列表失败:', error)
  }
}

// 添加分组名称映射方法
const getGroupNames = (groupIds: string[]) => {
  if (!groupIds?.length) return ''
  return groupIds
    .map(id => groupListOptions.value.find(group => group.id === id)?.name)
    .filter(Boolean)
    .join(', ')
}

// 关闭模态框并重置表单
const closeModal = () => {
  visible.value = false
  hostFormRef.value?.resetFields()
}

// 修改模态框状态（新增/编辑）
const modalType = ref<'add' | 'edit'>('add')

// 修改模态框标题计算属性
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加主机' : '编辑主机'
)

// 打开编辑主机模态框
const openEditModal = (record: HostsRecord) => {
  modalType.value = 'edit'
  visible.value = true
  // 克隆记录以避免直接修改表格数据
  hostFormData.value = { ...record }
}

// 修改提交表单方法
const submitHostForm = async () => {
  try {
    loading.value = true
    const res =
      modalType.value === 'add'
        ? await reqAddHosts(hostFormData.value)
        : await reqUpdateHosts(hostFormData.value)

    AMessage.success(
      res.message ||
        (modalType.value === 'add' ? '添加主机成功' : '编辑主机成功')
    )
    closeModal()
    getHostList()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

// 删除主机
const deleteHost = async (id: string) => {
  if (loading.value) return // 防止重复点击

  try {
    loading.value = true
    const res = await reqDeleteHosts(id)
    AMessage.success(res.message || '删除主机成功')
    await getHostList()
  } catch (error: any) {
    AMessage.error(error.message || '删除主机失败')
  } finally {
    loading.value = false
  }
}

// 修改打开添加模态框方法
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 下载数据
const downloadData = () => {
  // TODO: 实现下载功能
  AMessage.info('下载功能开发中')
}

// 硬件类型选项
const hardwareTypeOptions = [
  { label: '虚拟机', value: 'virtual' },
  { label: '实体机', value: 'physical' }
]

// 添加状态选项常量
const statusOptions = [
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' }
]

// 校验主机表单
const checkFormRule = async () => {
  const res = await hostFormRef.value.validate()
  return !res
}

// 修改获取主机列表的方法
const getHostList = async () => {
  try {
    const res = await reqGetHostsList(searchForm)
    tableData.value = res.data.hosts
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取主机列表失败')
  } finally {
  }
}

// 修改搜索方法
const handleSearch = () => {
  searchForm.page = 1
  sendQuery()
}

// 修改重置方法
const resetForm = () => {
  searchFormRef.value?.resetFields()
  // 重置所有查询参数
  searchForm.page = 1
  searchForm.page_size = 10
  searchForm.hostname = undefined
  searchForm.ip = undefined
  searchForm.os = undefined
  searchForm.location = undefined
  searchForm.groups = undefined
  searchForm.status = undefined
  searchForm.sort_by = undefined
  searchForm.sort_order = undefined
  sendQuery()
}

// 修改分页相关方法
const onChange = (current: number) => {
  searchForm.page = current
  sendQuery()
}

const onPageSizeChange = (size: number) => {
  searchForm.page_size = size
  searchForm.page = 1
  sendQuery()
}

// 修改刷新方法
const refreshData = () => {
  sendQuery()
  AMessage.success('刷新成功')
}

// 添加操作系统和位置选项的获取方法
const osOptions = ref<string[]>([])
const locationOptions = ref<string[]>([])

const getOsGroups = async () => {
  try {
    const res = await reqGetOsGroups()
    osOptions.value = res.data
  } catch (error: any) {
    console.error('获取操作系统选项失败:', error)
  }
}

const getLocationGroups = async () => {
  try {
    const res = await reqGetLocationGroups()
    locationOptions.value = res.data
  } catch (error: any) {
    console.error('获取位置选项失败:', error)
  }
}

// 添加路由跳转方法
const router = useRouter()

const goToHostDetail = (id: string) => {
  router.push({
    name: 'HostDetail', // 确保这个名称与你的路由配置匹配
    params: {
      id: id
    }
  })
}

// 添加 WebSocket 连接方法
const connectWebSocket = () => {
  // 确保有 token
  const token = userStore.token
  if (!token) {
    AMessage.error('未获取到认证信息')
    return
  }

  // 动态获取 WebSocket 连接地址
  const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://'
  const host = window.location.host // 获取当前主机地址
  const wsUrl = `${protocol}${host}/api/ws/hosts/watch?token=${token}` // 构建 WebSocket URL
  // 创建 WebSocket 连接
  ws.value = new WebSocket(wsUrl)

  // WebSocket 连接成功
  ws.value.onopen = () => {
    AMessage.success('实时通道已连接')
    // 连接成功后发送初始查询
    sendQuery()
  }

  // WebSocket 连接错误
  ws.value.onerror = error => {
    console.error('WebSocket 连接错误:', error)
    AMessage.error('WebSocket 连接失败')
  }

  // WebSocket 接收消息
  ws.value.onmessage = event => {
    try {
      const { data } = JSON.parse(event.data)
      // 更新表格数据
      if (data) {
        tableData.value = data.hosts
        total.value = data.total || 0
      }
    } catch (error) {
      console.error('解析 WebSocket 消息失败:', error)
    }
  }

  // WebSocket 连接关闭
  ws.value.onclose = event => {
    if (event.code === 1000) {
      AMessage.info('WebSocket 连接已正常关闭')
    } else if (event.code === 1006) {
      AMessage.error('WebSocket 连接异常关闭')
    } else if (event.code === 4001) {
      AMessage.error('WebSocket 认证失败')
    }
    ws.value = null
  }
}

// 发送查询数据
const sendQuery = () => {
  if (ws.value && ws.value.readyState === WebSocket.OPEN) {
    // 构建查询参数，只包含有值的字段
    const queryData: Record<string, any> = {
      page: searchForm.page,
      page_size: searchForm.page_size
    }

    // 添加其他查询参数（只添加有值的字段）
    if (searchForm.hostname) queryData.hostname = searchForm.hostname
    if (searchForm.ip) queryData.ip = searchForm.ip
    if (searchForm.os) queryData.os = searchForm.os
    if (searchForm.location) queryData.location = searchForm.location
    if (searchForm.groups?.length) queryData.groups = searchForm.groups
    if (searchForm.status) queryData.status = searchForm.status
    if (searchForm.sort_by) queryData.sort_by = searchForm.sort_by
    if (searchForm.sort_order) queryData.sort_order = searchForm.sort_order

    // console.log('发送查询参数:', queryData) // 用于调试
    ws.value.send(JSON.stringify(queryData))
  }
}

// 关闭 WebSocket 连接
const closeWebSocket = () => {
  if (ws.value && ws.value.readyState === WebSocket.OPEN) {
    ws.value.close()
    ws.value = null
  }
}

// 修改组件挂载时的行为
onMounted(() => {
  getGroupList()
  getOsGroups()
  getLocationGroups()
  connectWebSocket() // 建立 WebSocket 连接
})

// 修改组件卸载时的行为
onUnmounted(() => {
  closeWebSocket() // 关闭 WebSocket 连接
})
</script>

<template>
  <div class="container">
    <div v-if="loading" class="spin-overlay">
      <a-spin dot />
    </div>
    <!-- 搜索区域 -->
    <a-card class="general-card" title="主机资产">
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchFormRef"
            :model="searchForm"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="主机名称" field="hostname">
                  <a-input
                    v-model="searchForm.hostname"
                    placeholder="请输入主机名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="IP地址" field="ip">
                  <a-input
                    v-model="searchForm.ip"
                    placeholder="请输入IP地址"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="操作系统" field="os">
                  <a-select
                    v-model="searchForm.os"
                    placeholder="请选择操作系统"
                    allow-clear
                  >
                    <a-option v-for="os in osOptions" :key="os" :value="os">
                      {{ os }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="主机位置" field="location">
                  <a-select
                    v-model="searchForm.location"
                    placeholder="请选择主机位置"
                    allow-clear
                  >
                    <a-option
                      v-for="location in locationOptions"
                      :key="location"
                      :value="location"
                    >
                      {{ location }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="主机组" field="groups">
                  <a-select
                    v-model="searchForm.groups"
                    placeholder="请选择主机组"
                    multiple
                    allow-clear
                  >
                    <a-option
                      v-for="group in groupListOptions"
                      :key="group.id"
                      :value="group.id"
                    >
                      {{ group.name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态" field="status">
                  <a-select
                    v-model="searchForm.status"
                    placeholder="请选择状态"
                    allow-clear
                  >
                    <a-option
                      v-for="option in statusOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="resetForm">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-button @click="downloadData">
            <template #icon>
              <icon-material-symbols-download />
            </template>
            下载
          </a-button>

          <a-tooltip content="刷新">
            <div class="action-icon" @click="refreshData">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>

          <!-- 列设置下拉菜单 -->
          <a-tooltip content="列设置">
            <a-popover
              trigger="click"
              position="bl"
              @popup-visible-change="popupVisibleChange"
            >
              <div class="action-icon">
                <icon-uil-setting />
              </div>
              <template #content>
                <div id="tableSetting" class="column-settings">
                  <div
                    v-for="column in columnSettings"
                    :key="column.dataIndex"
                    class="column-item"
                  >
                    <div class="drag-handle">
                      <icon-ri-drag-move-2-fill />
                    </div>
                    <a-checkbox
                      v-model="column.checked"
                      @change="
                        (value, ev) => handleColumnChange(value, ev, column)
                      "
                    >
                      {{ column.title }}
                    </a-checkbox>
                  </div>
                </div>
              </template>
            </a-popover>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格区域 -->
      <a-table
        :bordered="false"
        :data="tableData"
        :columns="visibleColumns"
        :scroll="{ x: '1000px' }"
        :pagination="pagination"
        :loading="loading"
        @page-change="onChange"
        @page-size-change="onPageSizeChange"
        @sorter-change="onSortChange"
      >
        <!-- 主机名称 -->
        <template #hostname="{ record }">
          <span class="hostname">
            <span
              class="status-dot"
              :class="{
                'status-online': record.status === 'online',
                'status-offline': record.status === 'offline'
              }"
            />
            <a-link class="hostname-text" @click="goToHostDetail(record.id)">
              {{ record.hostname.split('.')[0] }}
            </a-link>
          </span>
        </template>

        <!-- 操作系统 -->
        <template #os="{ record }">
          <a-tooltip :content="record.os">
            <span class="os">
              <span class="icon-wrapper">
                <icon-mdi-ubuntu
                  class="icon-size"
                  v-if="record.os.toLowerCase().includes('ubuntu')"
                />
                <icon-mdi-microsoft-windows
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('windows')"
                />
                <icon-mdi-linux
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('linux')"
                />
                <icon-mdi-centos
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('centos')"
                />
                <icon-mdi-redhat
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('redhat')"
                />
                <icon-svg-openEuler
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('openeuler')"
                />
                <icon-svg-Anolis
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('anolis')"
                />
                <icon-svg-Kylin
                  class="icon-size"
                  v-else-if="record.os.toLowerCase().includes('kylin')"
                />
                <icon-uiw-linux class="icon-size" v-else />
              </span>
              <span class="os-text text-ellipsis">{{ record.os }}</span>
            </span>
          </a-tooltip>
        </template>

        <!-- 启动时间 -->
        <template #bootTime="{ record }">
          <span class="total-value">
            {{
              record.agent_metrics?.base_info?.boot_time !== undefined
                ? `${secondsToDays(record.agent_metrics.base_info.boot_time)} Days`
                : '-'
            }}
          </span>
        </template>

        <!-- CPU 核心数 -->
        <template #cpu_total="{ record }">
          <span class="total-value">
            {{ record.agent_metrics?.cpu_info?.total || record.cpu_total }}
            Core
          </span>
        </template>

        <!-- 内存大小 -->
        <template #memory_total="{ record }">
          <span class="total-value">
            {{
              record.agent_metrics?.mem_info?.total
                ? formatBytes(record.agent_metrics.mem_info.total)
                : formatGigaBytes(record.memory_total)
            }}
          </span>
        </template>

        <!-- 磁盘大小 -->
        <template #disk_total="{ record }">
          <span class="total-value">
            {{
              record.agent_metrics?.total_disk_info?.total
                ? formatBytes(record.agent_metrics.total_disk_info.total)
                : formatGigaBytes(record.disk_total)
            }}
          </span>
        </template>

        <!-- CPU 使用率 -->
        <template #cpu_usage="{ record }">
          <template v-if="record.agent_metrics?.cpu_info !== undefined">
            <div style="display: flex; align-items: center; gap: 8px">
              <a-progress
                :percent="record.agent_metrics?.cpu_info.used_percent / 100"
                status="normal"
                :show-text="false"
                :stroke-width="5"
                :color="
                  record.agent_metrics?.cpu_info.used_percent >= 90
                    ? 'rgb(var(--danger-6))'
                    : record.agent_metrics?.cpu_info.used_percent >= 80
                      ? 'rgb(var(--warning-6))'
                      : ''
                "
                style="flex: 1"
              />

              <span class="data-value">
                {{ Math.round(record.agent_metrics?.cpu_info.used_percent) }}%
              </span>
            </div>
          </template>
          <template v-else>
            <span class="data-value">-</span>
          </template>
        </template>

        <!-- 内存使用率 -->
        <template #memory_usage="{ record }">
          <template v-if="record.agent_metrics?.mem_info !== undefined">
            <div style="display: flex; align-items: center; gap: 8px">
              <a-progress
                :percent="record.agent_metrics?.mem_info.used_percent / 100"
                status="normal"
                :show-text="false"
                :stroke-width="5"
                :color="
                  record.agent_metrics?.mem_info.used_percent >= 90
                    ? 'rgb(var(--danger-6))'
                    : record.agent_metrics?.mem_info.used_percent >= 80
                      ? 'rgb(var(--warning-6))'
                      : ''
                "
                style="flex: 1"
              />

              <span class="data-value">
                {{ Math.round(record.agent_metrics?.mem_info.used_percent) }}%
              </span>
            </div>
          </template>
          <template v-else>
            <span class="data-value">-</span>
          </template>
        </template>

        <!-- 根分区使用率 -->
        <template #disk_root_usage="{ record }">
          <template v-if="record.agent_metrics?.partition_info?.length > 0">
            <div style="display: flex; align-items: center; gap: 8px">
              <a-tooltip
                :content="`根分区容量：${formatBytes(
                  record.agent_metrics?.partition_info.find(
                    (p: any) => p.mount_point === '/'
                  )?.total || 0
                )}`"
              >
                <a-progress
                  :percent="
                    (record.agent_metrics?.partition_info.find(
                      (p: any) => p.mount_point === '/'
                    )?.used_percent || 0) / 100
                  "
                  status="normal"
                  :stroke-width="5"
                  :show-text="false"
                  :color="
                    (record.agent_metrics?.partition_info.find(
                      (p: any) => p.mount_point === '/'
                    )?.used_percent || 0) >= 90
                      ? 'rgb(var(--danger-6))'
                      : (record.agent_metrics?.partition_info.find(
                            (p: any) => p.mount_point === '/'
                          )?.used_percent || 0) >= 80
                        ? 'rgb(var(--warning-6))'
                        : ''
                  "
                  style="flex: 1"
                />
              </a-tooltip>
              <span class="data-value">
                {{
                  Math.round(
                    record.agent_metrics?.partition_info.find(
                      (p: any) => p.mount_point === '/'
                    )?.used_percent || 0
                  )
                }}%
              </span>
            </div>
          </template>
          <template v-else>
            <span class="data-value">-</span>
          </template>
        </template>

        <!-- 网络下载速度 (入站流量) -->
        <template #netIn="{ record }">
          <template v-if="record.agent_metrics?.default_net_info !== undefined">
            <span class="total-value">
              {{
                formatSpeed(record.agent_metrics?.default_net_info.net_in_speed)
              }}
            </span>
          </template>
          <template v-else>
            <span class="data-value">-</span>
          </template>
        </template>

        <!-- 网络上传速度 (出站流量) -->
        <template #netOut="{ record }">
          <template v-if="record.agent_metrics?.default_net_info !== undefined">
            <span class="total-value">
              {{
                formatSpeed(
                  record.agent_metrics?.default_net_info.net_out_speed
                )
              }}
            </span>
          </template>
          <template v-else>
            <span class="data-value">-</span>
          </template>
        </template>

        <!-- 操作按钮 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-popconfirm
              :content="`确定要删除主机 ${record.hostname} 吗？`"
              type="warning"
              @ok="deleteHost(record.id)"
            >
              <a-button type="text" size="mini" status="danger">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <a-modal
      width="600px"
      v-model:visible="visible"
      :title="modalTitle"
      @cancel="closeModal"
      @ok="submitHostForm"
      :on-before-ok="checkFormRule"
    >
      <a-form
        ref="hostFormRef"
        :model="hostFormData"
        :rules="formRules"
        label-align="right"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 18 }"
        size="small"
      >
        <a-form-item label="主机名称" field="hostname">
          <a-input
            v-model="hostFormData.hostname"
            placeholder="请输入主机名称"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="硬件类型" field="hardware_type">
          <a-select
            v-model="hostFormData.hardware_type"
            placeholder="请选择硬件类型"
            :default-value="'virtual'"
          >
            <a-option
              v-for="option in hardwareTypeOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="IP地址" field="ip">
          <a-input-tag
            v-model="hostFormData.ip"
            placeholder="请输入IP地址后按回车键添加"
            allow-clear
            :unique="true"
            :max-tag-count="5"
          />
        </a-form-item>

        <a-form-item label="操作系统" field="os">
          <a-auto-complete
            v-model="hostFormData.os"
            :data="osOptions"
            placeholder="请输入或选择操作系统"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="CPU（Core）" field="cpu_total">
          <a-input-number
            v-model="hostFormData.cpu_total"
            placeholder="请输入CPU核心数"
            :min="1"
            :precision="0"
          >
          </a-input-number>
        </a-form-item>

        <a-form-item label="内存（GB）" field="memory_total">
          <a-input-number
            v-model="hostFormData.memory_total"
            placeholder="请输入内存大小"
            :min="1"
            :precision="0"
          >
          </a-input-number>
        </a-form-item>

        <a-form-item label="磁盘（GB）" field="disk_total">
          <a-input-number
            v-model="hostFormData.disk_total"
            placeholder="请输入磁盘大小"
            :min="0"
            :precision="0"
          >
          </a-input-number>
        </a-form-item>

        <a-form-item label="位置" field="location">
          <a-auto-complete
            v-model="hostFormData.location"
            :data="locationOptions"
            placeholder="请输入或选择位置"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="主机组" field="groups">
          <a-select
            v-model="hostFormData.groups"
            placeholder="请选择主机组"
            multiple
            allow-clear
            :validate-status="false"
          >
            <a-option
              v-for="group in groupListOptions"
              :key="group.id"
              :value="group.id"
            >
              {{ group.name }}
            </a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="描述" field="description">
          <a-textarea
            v-model="hostFormData.description"
            placeholder="请输入描述信息"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped lang="scss">
.os {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.os-text {
  flex: 1;
  min-width: 0;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.icon-wrapper :deep(svg) {
  vertical-align: middle;
}

.icon-size {
  width: 16px;
  height: 16px;
}

.hostname {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 7px;
  height: 7px;
  border-radius: 50%;
}

.status-online {
  background-color: rgb(var(--success-6));
}

.status-offline {
  background-color: rgb(var(--danger-6));
}

.hostname-text {
  color: var(--color-neutral-10);
  font-weight: 600;
}

.total-value {
  font-size: 13px;
  font-weight: 500;
}

.data-value {
  font-size: 12px;
  font-weight: 500;
}

.action-icon {
  margin-left: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.setting {
  display: flex;
  align-items: center;
  width: 200px;

  .title {
    margin-left: 12px;
    cursor: pointer;
  }
}

.column-settings {
  min-width: 200px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px 0;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 2px 10px;

  &:hover {
    background-color: var(--color-fill-2);
  }

  .drag-handle {
    margin-right: 8px;
    cursor: move;
    display: flex;
    align-items: center;
  }

  :deep(.arco-checkbox) {
    flex: 1;
  }

  :deep(.arco-checkbox-label) {
    flex: 1;
  }
}
</style>
