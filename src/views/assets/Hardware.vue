<script setup lang="ts">
import type {
  HardwareRecord,
  HardwareListParams,
  HardwareInputGroup
} from '@/types/hardware'
import type { FieldRule, TableColumnData } from '@arco-design/web-vue'
import {
  reqGetHardwareList,
  reqAddHardware,
  reqUpdateHardware,
  reqDeleteHardware,
  reqGetHardwareInputGroup
} from '@/api/hardware'

// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<HardwareListParams>({
  page: 1,
  page_size: 10
})

// 表格列配置
const columns: TableColumnData[] = [
  {
    title: '硬件名称',
    dataIndex: 'name',
    width: 280,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '设备厂商',
    dataIndex: 'manufacturer'
  },
  {
    title: '管理地址',
    dataIndex: 'manager_address'
  },
  {
    title: '设备类型',
    dataIndex: 'type',
    slotName: 'type'
  },
  {
    title: '机房位置',
    dataIndex: 'location'
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status'
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 200,
    fixed: 'right'
  }
]

// 加载状态标识
const loading = ref(false)
// 数据总条数
const total = ref(0)
// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 硬件列表数据
const hardwareList = ref<HardwareRecord[]>([])

// 获取硬件列表数据
const getHardwareList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    // TODO: 替换为实际的 API 请求
    const res = await reqGetHardwareList(params)
    hardwareList.value = res.data.hardwares
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取硬件列表失败')
  } finally {
    loading.value = false
  }
}

// IP地址校验规则
const validateIP = (value: string) => {
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(:\d{1,5})?$/
  return ipRegex.test(value)
}

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  name: [{ required: true, message: '请输入硬件名称' }],
  type: [{ required: true, message: '请选择设备类型' }],
  model: [{ required: true, message: '请输入设备型号' }],
  manufacturer: [{ required: true, message: '请输入设备厂商' }],
  location: [{ required: true, message: '请输入机房位置' }],
  size: [
    {
      required: true,
      message: '请输入设备尺寸'
    },
    {
      validator: (value, cb) => {
        if (value && !/^\d+U$/.test(value)) {
          cb('设备尺寸格式必须为数字+大写U，如：2U')
        }
        cb()
      }
    }
  ],
  status: [{ required: true, message: '请选择设备状态' }],
  manager_address: [
    {
      validator: (value, cb) => {
        if (value && !validateIP(value)) {
          cb('请输入正确的IP地址格式')
        }
        cb()
      }
    }
  ],
  business_address: [
    {
      validator: (value, cb) => {
        if (value && !validateIP(value)) {
          cb('请输入正确的IP地址格式')
        }
        cb()
      }
    }
  ]
}

// 模态框显示状态
const visible = ref(false)
// 模态框操作类型
const modalType = ref<'add' | 'edit'>('add')
// 硬件表单数据
const hardwareForm = reactive<HardwareRecord>({
  name: '',
  type: 'server',
  model: '',
  location: '',
  rack_location: '',
  size: '',
  manager_address: '',
  business_address: '',
  manufacturer: '',
  purchase_date: '',
  warranty_date: '',
  responsible_person: '',
  description: '',
  status: 'enable'
})

// 设备类型选项
const typeOptions = [
  { label: '服务器', value: 'server' },
  { label: '网络设备', value: 'network' },
  { label: '安全设备', value: 'safety' },
  { label: '存储设备', value: 'storage' },
  { label: '其他设备', value: 'other' }
]

// 表单实例
const hardwareFormRef = ref()

// 计算模态框标题
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加硬件' : '编辑硬件'
)

// 打开添加模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: HardwareRecord) => {
  modalType.value = 'edit'
  Object.assign(hardwareForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  hardwareFormRef.value?.resetFields()
}

// 表单提交前校验
const checkFormRule = async () => {
  const res = await hardwareFormRef.value.validate()
  return !res
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getHardwareList()
}

// 添加输入提示数据
const inputGroup = ref<HardwareInputGroup>({
  locations: [],
  responsible_persons: [],
  sizes: []
})

// 获取输入提示数据
const getInputGroup = async () => {
  try {
    const res = await reqGetHardwareInputGroup()
    inputGroup.value = res.data
  } catch (error: any) {
    AMessage.error(error.message || '获取输入提示失败')
  }
}

// 组件挂载时获取列表
onMounted(() => {
  getHardwareList()
  getInputGroup()
})

// 在 handleSubmit 函数前添加一个处理函数移除字符串两端的空格
const trimFormFields = (form: HardwareRecord): HardwareRecord => {
  const trimmedForm: Partial<HardwareRecord> = { ...form }
  Object.keys(trimmedForm).forEach(key => {
    const value = trimmedForm[key as keyof HardwareRecord]
    if (typeof value === 'string') {
      trimmedForm[key as keyof HardwareRecord] = value.trim() as any
    }
  })
  return trimmedForm as HardwareRecord
}

// 修改 handleSubmit 函数
const handleSubmit = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const trimmedForm = trimFormFields(hardwareForm)
    modalType.value === 'add'
      ? await reqAddHardware(trimmedForm)
      : await reqUpdateHardware(trimmedForm)
    AMessage.success(
      modalType.value === 'add' ? '添加硬件成功' : '编辑硬件成功'
    )
    closeModal()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
    getHardwareList()
    getInputGroup()
  }
}

// 删除硬件
const handleDelete = async (record: HardwareRecord) => {
  if (loading.value) return
  try {
    loading.value = true
    await reqDeleteHardware(record.id as string)
    AMessage.success('删除硬件成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除硬件失败')
  } finally {
    loading.value = false
    getHardwareList()
    getInputGroup()
  }
}

// 分页相关处理函数
const onPageChange = (current: number) => {
  params.page = current
  getHardwareList()
}

const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getHardwareList()
}

// 详情抽屉控制
const drawerVisible = ref(false)
// 当前查看的硬件详情
const currentHardware = ref<HardwareRecord | null>(null)

// 打开详情抽屉
const openDrawer = (record: HardwareRecord) => {
  currentHardware.value = { ...record }
  drawerVisible.value = true
}

// 关闭详情抽屉
const closeDrawer = () => {
  drawerVisible.value = false
  currentHardware.value = null
}
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="硬件资产">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="硬件名称" field="name">
                  <a-input
                    v-model="params.name"
                    placeholder="请输入硬件名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="设备类型" field="type">
                  <a-select
                    v-model="params.type"
                    placeholder="请选择设备类型"
                    allow-clear
                  >
                    <a-option
                      v-for="item in typeOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="机房位置" field="location">
                  <a-select
                    v-model="params.location"
                    placeholder="请选择机房位置"
                    allow-clear
                  >
                    <a-option
                      v-for="location in inputGroup.locations"
                      :key="location"
                      :value="location"
                    >
                      {{ location }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="管理地址" field="manager_address">
                  <a-input
                    v-model="params.manager_address"
                    placeholder="请输入管理地址"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="业务地址" field="business_address">
                  <a-input
                    v-model="params.business_address"
                    placeholder="请输入业务地址"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="设备状态" field="status">
                  <a-select
                    v-model="params.status"
                    placeholder="请选择设备状态"
                    allow-clear
                  >
                    <a-option value="enable">使用中</a-option>
                    <a-option value="disable">未使用</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="getHardwareList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getHardwareList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="hardwareList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #type="{ record }">
          {{ typeOptions.find(item => item.value === record.type)?.label }}
        </template>
        <template #status="{ record }">
          <div class="status-wrapper">
            <div class="status-icon">
              <icon-grommet-icons-status-good-small
                :color="record.status === 'enable' ? '#00a245' : '#e00101'"
              />
            </div>
            <span v-if="record.status === 'enable'">使用中</span>
            <span v-else-if="record.status === 'disable'">未使用</span>
            <span v-else>未知</span>
          </div>
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="mini" @click="openDrawer(record)">
              详情
            </a-button>
            <a-popconfirm
              content="确定要删除该硬件吗？"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="hardwareFormRef"
          :model="hardwareForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="硬件名称" field="name">
            <a-input v-model="hardwareForm.name" placeholder="请输入硬件名称" />
          </a-form-item>
          <a-form-item label="设备类型" field="type">
            <a-select v-model="hardwareForm.type" placeholder="请选择设备类型">
              <a-option
                v-for="item in typeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="设备型号" field="model">
            <a-input v-model="hardwareForm.model" placeholder="如: Dell R740" />
          </a-form-item>
          <a-form-item label="设备厂商" field="manufacturer">
            <a-input
              v-model="hardwareForm.manufacturer"
              placeholder="如: Dell、华为、浪潮"
            />
          </a-form-item>
          <a-form-item label="设备尺寸" field="size">
            <a-auto-complete
              v-model="hardwareForm.size"
              :data="inputGroup.sizes"
              placeholder="如: 2U、4U（必须大写U）"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="机房位置" field="location">
            <a-auto-complete
              v-model="hardwareForm.location"
              :data="inputGroup.locations"
              placeholder="如: A区机房、B区机房"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="机架位置" field="rack_location">
            <a-input
              v-model="hardwareForm.rack_location"
              placeholder="如: A01、B02"
            />
          </a-form-item>

          <a-form-item label="管理地址" field="manager_address">
            <a-input
              v-model="hardwareForm.manager_address"
              placeholder="如: ***********"
            />
          </a-form-item>

          <a-form-item label="业务地址" field="business_address">
            <a-input
              v-model="hardwareForm.business_address"
              placeholder="如: ***********:8080"
            />
          </a-form-item>
          <a-form-item label="设备状态" field="status">
            <a-select
              v-model="hardwareForm.status"
              placeholder="请选择设备状态"
            >
              <a-option value="enable">使用中</a-option>
              <a-option value="disable">未使用</a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="负责人" field="responsible_person">
            <a-auto-complete
              v-model="hardwareForm.responsible_person"
              :data="inputGroup.responsible_persons"
              placeholder="如: 张三-***********"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="购买日期" field="purchase_date">
            <a-date-picker
              v-model="hardwareForm.purchase_date"
              style="width: 100%"
              placeholder="请选择购买日期"
            />
          </a-form-item>
          <a-form-item label="保修日期" field="warranty_date">
            <a-date-picker
              v-model="hardwareForm.warranty_date"
              style="width: 100%"
              placeholder="请选择保修日期"
            />
          </a-form-item>

          <a-form-item label="描述信息" field="description">
            <a-textarea
              v-model="hardwareForm.description"
              placeholder="请输入设备的用途、配置信息等描述"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <a-drawer
        :visible="drawerVisible"
        @cancel="closeDrawer"
        :width="400"
        :footer="false"
        unmountOnClose
      >
        <template #title>
          <a-space>
            <icon-tabler-server-cog />
            硬件详情
          </a-space>
        </template>
        <div class="drawer-content">
          <a-descriptions
            title="基本信息"
            size="small"
            :column="1"
            :data="[
              {
                label: '硬件名称',
                value: currentHardware?.name || '-'
              },
              {
                label: '设备类型',
                value:
                  typeOptions.find(item => item.value === currentHardware?.type)
                    ?.label || '-'
              },
              {
                label: '设备型号',
                value: currentHardware?.model || '-'
              },
              {
                label: '设备厂商',
                value: currentHardware?.manufacturer || '-'
              },
              {
                label: '设备尺寸',
                value: currentHardware?.size || '-'
              },
              {
                label: '机房位置',
                value: currentHardware?.location || '-'
              },
              {
                label: '机架位置',
                value: currentHardware?.rack_location || '-'
              }
            ]"
          />

          <a-descriptions
            title="网络信息"
            size="small"
            :column="1"
            :data="[
              {
                label: '管理地址',
                value: currentHardware?.manager_address || '-'
              },
              {
                label: '业务地址',
                value: currentHardware?.business_address || '-'
              }
            ]"
            style="margin-top: 24px"
          />

          <a-descriptions
            title="维护信息"
            size="small"
            :column="1"
            :data="[
              {
                label: '负责人',
                value: currentHardware?.responsible_person || '-'
              },
              {
                label: '设备状态',
                value: currentHardware?.status === 'enable' ? '启用' : '禁用'
              },
              {
                label: '购买日期',
                value: currentHardware?.purchase_date || '-'
              },
              {
                label: '保修日期',
                value: currentHardware?.warranty_date || '-'
              }
            ]"
            style="margin-top: 24px"
          />

          <a-descriptions
            title="其他信息"
            :column="1"
            :data="[
              {
                label: '描述信息',
                value: currentHardware?.description || '暂无描述'
              }
            ]"
            style="margin-top: 24px"
          />
        </div>
      </a-drawer>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

// 添加描述列表标题样式
:deep(.arco-descriptions-title) {
  font-size: 14px;
  color: var(--color-neutral-8);
  font-weight: bold;
  margin-bottom: 8px;
}

// 调整标签和内容的字体大小
:deep(.arco-descriptions-item-label),
:deep(.arco-descriptions-item-value) {
  font-size: 13px;
  font-weight: 500;
}

.status-wrapper {
  display: flex;
  align-items: center;
}

.status-icon {
  display: flex;
  align-items: center;
  font-size: 5px;
  margin-right: 4px;
}
</style>
