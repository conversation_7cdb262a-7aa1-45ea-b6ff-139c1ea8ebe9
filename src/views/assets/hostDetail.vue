<script lang="ts" setup>
import { formatBytes, formatTimestamp, secondsToDays } from '@/utils/format'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'echarts/charts'
import {
  Grid<PERSON>omponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'

use([
  Canvas<PERSON>enderer,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
])

import type { HostsRecord } from '@/types/hosts'

import useSystemStore from '@/stores/modules/system'
import useUserStore from '@/stores/modules/user'

const route = useRoute()
const router = useRouter()
const id = route.params.id as string
const hostInfo = ref<HostsRecord | null>(null)

const loading = ref(true)

const userStore = useUserStore()
const ws = ref<WebSocket | null>(null)

// 添加 WebSocket 连接方法
const connectWebSocket = () => {
  // 确保有 token
  const token = userStore.token
  if (!token) {
    AMessage.error('未获取到认证信息')
    return
  }

  // 动态获取 WebSocket 连接地址
  const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://'
  const host = window.location.host // 获取当前主机地址
  const wsUrl = `${protocol}${host}/api/ws/hosts/detail/watch?token=${token}` // 构建 WebSocket URL
  // 创建 WebSocket 连接
  ws.value = new WebSocket(wsUrl)

  // WebSocket 连接成功
  ws.value.onopen = () => {
    AMessage.success('实时通道已连接')
    // 连接成功后发送主机 ID
    sendHostId()
  }

  // WebSocket 连接错误
  ws.value.onerror = error => {
    console.error('WebSocket 连接错误:', error)
    AMessage.error('WebSocket 连接失败')
  }

  // WebSocket 接收消息
  ws.value.onmessage = event => {
    try {
      const { data } = JSON.parse(event.data)
      // 更新主机详情数据
      if (data) {
        hostInfo.value = data
        loading.value = false
      }
    } catch (error) {
      console.error('解析 WebSocket 消息失败:', error)
      loading.value = false
    }
  }

  // WebSocket 连接关闭
  ws.value.onclose = event => {
    if (event.code === 1000) {
      AMessage.info('WebSocket 连接已正常关闭')
    } else if (event.code === 1006) {
      AMessage.error('WebSocket 连接异常关闭')
    } else if (event.code === 4001) {
      AMessage.error('WebSocket 认证失败')
    }
    ws.value = null
  }
}

// 发送主机 ID
const sendHostId = () => {
  if (ws.value && ws.value.readyState === WebSocket.OPEN) {
    ws.value.send(JSON.stringify({ id }))
  }
}

// 关闭 WebSocket 连接
const closeWebSocket = () => {
  if (ws.value && ws.value.readyState === WebSocket.OPEN) {
    ws.value.close()
    ws.value = null
  }
}

// 返回主机列表
const goBack = () => {
  router.push({ name: 'HostsAssets' })
}

onMounted(() => {
  loading.value = true
  connectWebSocket() // 建立 WebSocket 连接
})

// 修改组件卸载时的行为
onUnmounted(() => {
  closeWebSocket() // 关闭 WebSocket 连接
})
const systemStore = useSystemStore()
// 获取当前主题模式
const isDarkMode = computed(() => systemStore.currentMode?.name === 'dark')

const position = ref('overview')

// 添加响应式数据
const selectedDisk = ref(
  hostInfo.value?.agent_metrics?.partition_info?.[0]?.mount_point || '/'
)
const selectedInterface = ref('')

// 监听 hostInfo 变化，自动选择第一个网络接口
watch(
  () => hostInfo.value?.agent_metrics?.net_info,
  newNetInfo => {
    if (newNetInfo && newNetInfo.length > 0 && !selectedInterface.value) {
      selectedInterface.value = newNetInfo[0].interface_name
    }
  },
  { immediate: true }
)

// 计算属性：获取当前选中的磁盘信息
const currentDiskInfo = computed(() => {
  return hostInfo.value?.agent_metrics?.partition_info?.find(
    disk => disk.mount_point === selectedDisk.value
  )
})

// 计算属性：获取当前选中的网络接口信息
const currentNetInfo = computed(() => {
  return hostInfo.value?.agent_metrics?.net_info?.find(
    net => net.interface_name === selectedInterface.value
  )
})

// 网络饼图配置
const networkChartConfig = computed<EChartsOption>(() => {
  const outTransfer = currentNetInfo.value?.net_out_transfer || 0
  const inTransfer = currentNetInfo.value?.net_in_transfer || 0

  return {
    tooltip: {
      trigger: 'item',
      formatter: params => {
        const { name, value } = params as any
        return `${name}: ${formatBytes(value)}`
      },
      backgroundColor: 'white',
      borderWidth: 1,
      borderColor: '#e5e6eb',
      padding: [10, 14],
      textStyle: {
        color: '#1d2129',
        fontSize: 14
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['65%', '85%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        data: [
          {
            name: 'Total Transmitted',
            value: outTransfer,
            itemStyle: {
              color: '#6B74E6'
            }
          },
          {
            name: 'Total Received',
            value: inTransfer,
            itemStyle: {
              color: '#4CD263'
            }
          }
        ]
      }
    ]
  }
})

// 计算 tooltip 的样式，根据当前主题模式自动切换颜色
const tooltipStyle = computed(() => ({
  backgroundColor: isDarkMode.value ? 'var(--color-bg-2)' : 'white',
  textStyle: {
    color: 'var(--color-text-1)'
  },
  borderColor: 'var(--color-neutral-3)'
}))

// CPU 图表数据和配置
const cpuChartData = ref<{ time: string; value: number }[]>([])
const cpuChartOption = computed<EChartsOption>(() => {
  return {
    // 图表标题配置
    title: {
      text: 'CPU Activity',
      show: true,
      left: 20,
      top: 10,
      textStyle: {
        color: isDarkMode.value ? '#fff' : '#1D2129',
        fontSize: 16,
        fontWeight: 600
      }
    },
    // 图表网格配置
    grid: {
      top: 50,
      right: 20,
      bottom: 30,
      left: 50
    },
    // 隐藏图例
    legend: {
      show: false
    },
    // X轴配置
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: cpuChartData.value.map(item => item.time),
      axisLabel: {
        fontSize: 12,
        color: isDarkMode.value ? '#fff' : 'var(--color-text-2)',
        // 控制显示的刻度数量，每4个点显示一个
        interval: Math.floor(cpuChartData.value.length / 4),
        showMaxLabel: true,
        showMinLabel: true
      },
      // 刻度线样式
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      },
      // 坐标轴线样式
      axisLine: {
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      }
    },
    // Y轴配置
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: {
        show: true,
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: isDarkMode.value ? '#fff' : 'var(--color-text-2)',
        formatter: '{value}%'
      }
    },
    // 提示框配置
    tooltip: {
      trigger: 'axis',
      alwaysShowContent: true, // 始终显示提示框
      axisPointer: {
        type: 'line',
        animation: false,
        lineStyle: {
          color: 'var(--color-neutral-3)',
          type: 'dashed'
        }
      },
      backgroundColor: tooltipStyle.value.backgroundColor,
      borderWidth: 1,
      borderColor: tooltipStyle.value.borderColor,
      padding: [8, 12],
      borderRadius: 4,
      textStyle: tooltipStyle.value.textStyle,
      formatter: params => {
        const [param] = params as any[]
        return `<div style="margin-bottom: 4px; color: var(--color-text-1)">${param.name}</div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 6px; height: 6px; border-radius: 50%; background-color: #6B74E6; margin-right: 8px;"></span>
                  <span style="color: var(--color-text-2)">Usage</span>
                  <span style="margin-left: 12px; font-weight: 500; color: var(--color-text-1)">${param.value}%</span>
                </div>`
      }
    },
    // 数据系列配置
    series: [
      {
        type: 'line',
        name: 'CPU Usage',
        data: cpuChartData.value.map(item => item.value),
        smooth: true, // 启用平滑曲线
        showSymbol: false, // 不显示数据点标记
        // 线条样式
        lineStyle: {
          width: 2,
          color: '#6B74E6'
        },
        // 区域填充样式
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(107, 116, 230, 0.2)' // 渐变起始色
              },
              {
                offset: 1,
                color: 'rgba(107, 116, 230, 0.01)' // 渐变结束色
              }
            ]
          }
        }
      }
    ]
  }
})

// 更新 CPU 图表数据
const updateCpuChart = () => {
  const now = new Date()
  // 格式化当前时间为 HH:mm:ss
  const time = `${now.getHours().toString().padStart(2, '0')}:${now
    .getMinutes()
    .toString()
    .padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  // 获取当前 CPU 使用率，如果没有数据则默认为 0
  const cpuUsage = hostInfo.value?.agent_metrics?.cpu_info?.used_percent || 0

  // 添加新的数据点到图表数据中
  cpuChartData.value.push({ time, value: Number(cpuUsage.toFixed(1)) })
}

// 网络图表数据和配置
const networkChartData = ref<{ time: string; tx: number; rx: number }[]>([])
const networkChartOption = computed<EChartsOption>(() => {
  // 计算最大值来动态设置 Y 轴范围，确保图表显示合适
  const maxValue = Math.max(
    ...networkChartData.value.map(item => Math.max(item.tx, item.rx))
  )
  const yAxisMax = maxValue === 0 ? 100 : Math.ceil(maxValue * 1.2)

  return {
    title: {
      text: 'Network Activity',
      show: true,
      left: 20,
      top: 10,
      textStyle: {
        color: isDarkMode.value ? '#fff' : '#1D2129',
        fontSize: 16,
        fontWeight: 600
      }
    },
    grid: {
      top: 70,
      right: 80,
      bottom: 40,
      left: 80
    },
    legend: {
      data: ['TX', 'RX'],
      top: 50,
      right: 20,
      textStyle: {
        color: isDarkMode.value ? '#fff' : 'var(--color-text-2)',
        fontSize: 12
      },
      icon: 'circle',
      itemWidth: 15,
      itemHeight: 15,
      selectedMode: false,
      itemStyle: {
        borderWidth: 0
      },
      textAlign: 'left',
      itemGap: 16
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: networkChartData.value.map(item => item.time),
      axisLabel: {
        fontSize: 12,
        color: isDarkMode.value ? '#fff' : 'var(--color-text-2)',
        interval: Math.floor(networkChartData.value.length / 6),
        showMaxLabel: true,
        showMinLabel: true
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      },
      axisLine: {
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: yAxisMax,
      nameGap: 40,
      splitNumber: 5,
      minInterval: 1,
      axisLine: {
        show: true,
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        lineStyle: {
          color: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.2)'
            : 'var(--color-neutral-3)'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: isDarkMode.value ? '#fff' : 'var(--color-text-2)',
        formatter: value => {
          const formatted = formatBytes(Number(value))
          if (formatted === '0 B') return '0 B/s'
          return formatted.replace(' ', '') + '/s'
        },
        margin: 16,
        align: 'right'
      }
    },
    tooltip: {
      trigger: 'axis',
      alwaysShowContent: true,
      axisPointer: {
        type: 'line',
        animation: false,
        lineStyle: {
          color: 'var(--color-neutral-3)',
          type: 'dashed'
        }
      },
      backgroundColor: tooltipStyle.value.backgroundColor,
      borderWidth: 1,
      borderColor: tooltipStyle.value.borderColor,
      padding: [8, 12],
      borderRadius: 4,
      textStyle: tooltipStyle.value.textStyle,
      formatter: params => {
        const [tx, rx] = params as any[]
        return `<div style="margin-bottom: 4px; color: var(--color-text-1)">${tx.name}</div>
                <div style="display: flex; align-items: center; margin-bottom: 4px">
                  <span style="display: inline-block; width: 6px; height: 6px; border-radius: 50%; background-color: #6B74E6; margin-right: 8px;"></span>
                  <span style="color: var(--color-text-2)">TX</span>
                  <span style="margin-left: 12px; font-weight: 500; color: var(--color-text-1)">${formatBytes(tx.value)}/s</span>
                </div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 6px; height: 6px; border-radius: 50%; background-color: #4CD263; margin-right: 8px;"></span>
                  <span style="color: var(--color-text-2)">RX</span>
                  <span style="margin-left: 12px; font-weight: 500; color: var(--color-text-1)">${formatBytes(rx.value)}/s</span>
                </div>`
      }
    },
    series: [
      {
        name: 'TX',
        type: 'line',
        data: networkChartData.value.map(item => item.tx),
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#6B74E6'
        },
        itemStyle: {
          color: '#6B74E6'
        }
      },
      {
        name: 'RX',
        type: 'line',
        data: networkChartData.value.map(item => item.rx),
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#4CD263'
        },
        itemStyle: {
          color: '#4CD263'
        }
      }
    ]
  }
})

// 更新网络图表数据
const updateNetworkChart = () => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now
    .getMinutes()
    .toString()
    .padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  // 获取当前网络速度，如果没有数据则默认为 0
  const tx = currentNetInfo.value?.net_out_speed || 0
  const rx = currentNetInfo.value?.net_in_speed || 0

  // 添加新的数据点到图表数据中
  networkChartData.value.push({ time, tx, rx })
}

// 定时器引用，用于定期更新图表数据
let chartTimer: any = null

// 组件挂载时启动定时器，每秒更新一次数据
onMounted(() => {
  chartTimer = setInterval(() => {
    updateCpuChart()
    updateNetworkChart()
  }, 1000)
})

// 组件卸载时清除定时器，防止内存泄漏
onUnmounted(() => {
  if (chartTimer) {
    clearInterval(chartTimer)
    chartTimer = null
  }
})
</script>

<template>
  <div class="container">
    <div v-if="loading" class="spin-overlay">
      <a-spin dot />
    </div>
    <a-card class="general-card" :bordered="false">
      <!-- 顶部导航 -->
      <template #title>
        <div class="host-title-container">
          <a-space>
            <a-button type="text" shape="circle" @click="goBack">
              <icon-mdi-arrow-back-circle class="back-button" />
            </a-button>
            <span class="host-title">{{
              hostInfo?.hostname || '主机详情'
            }}</span>
          </a-space>
        </div>
      </template>

      <!-- 空状态显示 -->
      <template v-if="!hostInfo">
        <a-empty />
      </template>

      <!-- 有数据时显示内容 -->
      <template v-else>
        <!-- 主要内容区域 -->
        <div v-if="hostInfo" class="host-info-container">
          <a-descriptions
            :column="{ xs: 1, sm: 2, md: 3, lg: 4, xl: 5, xxl: 6 }"
            size="mini"
            label-align="left"
            table-layout="fixed"
            layout="inline-vertical"
            :label-style="{
              color: 'var(--color-text-3)',
              fontSize: '13px',
              paddingRight: '15px'
            }"
            :value-style="{
              color: 'var(--color-text-1)',
              fontSize: '13px',
              fontWeight: 500,
              paddingBottom: '10px'
            }"
          >
            <a-descriptions-item label="主机状态">
              <a-tag
                v-if="hostInfo.status === 'online'"
                size="small"
                color="green"
              >
                {{ hostInfo.status }}
              </a-tag>
              <a-tag v-else size="small" color="red">
                {{ hostInfo.status }}
              </a-tag>
            </a-descriptions-item>

            <a-descriptions-item label="IP 地址">
              <span>{{ hostInfo.ip.join(', ') || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="启动时间">
              <span>
                {{
                  hostInfo.agent_metrics?.base_info?.boot_time !== undefined
                    ? `${secondsToDays(hostInfo.agent_metrics.base_info.boot_time)} Days`
                    : '-'
                }}
              </span>
            </a-descriptions-item>

            <a-descriptions-item label="操作系统">
              <span>{{ hostInfo.os || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="架构">
              <span>{{ hostInfo.agent_metrics?.base_info?.arch || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="CPU 型号">
              <span>{{ hostInfo.agent_metrics?.cpu_info?.model || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="CPU Core">
              <span>{{ hostInfo.agent_metrics?.cpu_info?.total || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="Memory">
              <span>{{
                formatBytes(hostInfo.agent_metrics?.mem_info?.total || 0) || '-'
              }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="磁盘总大小">
              <span>{{
                formatBytes(
                  hostInfo.agent_metrics?.total_disk_info.total || 0
                ) || '-'
              }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="内核版本">
              <span>{{ hostInfo.agent_metrics?.base_info.kernel || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="位置">
              <span>{{ hostInfo.location || '-' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="最后更新时间">
              <span>{{
                formatTimestamp(
                  hostInfo.agent_metrics?.last_update_time as number
                )
              }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider orientation="center">
          <a-radio-group v-model="position" type="button">
            <a-radio value="overview">概览</a-radio>
            <a-radio value="diskAndNetwork">磁盘&网络</a-radio>
          </a-radio-group>
        </a-divider>

        <template v-if="position === 'overview' && hostInfo.agent_metrics">
          <div class="overview-container">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-card class="overview-card">
                  <div class="metric-card">
                    <div class="title">CPU</div>
                    <div class="main-value">
                      {{
                        (
                          hostInfo?.agent_metrics?.cpu_info?.used_percent || 0
                        ).toFixed(1)
                      }}%
                    </div>
                    <div class="sub-info">
                      Load Average:
                      {{
                        hostInfo?.agent_metrics?.base_info?.load_avg?.join(
                          ' | '
                        ) || '0 | 0 | 0'
                      }}
                    </div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card class="overview-card">
                  <div class="metric-card memory-card">
                    <a-row :gutter="16">
                      <!-- 左侧信息区域 -->
                      <a-col :span="12">
                        <div class="title">Memory</div>
                        <div class="main-value">
                          {{
                            (
                              hostInfo?.agent_metrics?.mem_info?.used_percent ||
                              0
                            ).toFixed(1)
                          }}%
                        </div>
                        <div class="sub-info">
                          {{
                            formatBytes(
                              hostInfo?.agent_metrics?.mem_info?.total || 0
                            )
                          }}
                          of Total
                        </div>
                      </a-col>
                      <!-- 右侧图表区域 -->
                      <a-col :span="12">
                        <!-- <div class="chart-container">
                          <VueDataUi
                            component="VueUiDonut"
                            :config="memoryChartConfig"
                            :dataset="memoryChartDataset"
                          />
                        </div> -->
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card class="overview-card">
                  <div class="network-panel">
                    <a-row :gutter="16">
                      <!-- 实时网速面板 -->
                      <a-col :span="12">
                        <div class="network-section">
                          <div class="section-title">Network</div>
                          <div class="network-metrics">
                            <div class="metric-item">
                              <a-tag color="purple" class="network-tag">
                                <template #icon>
                                  <icon-streamline-upload-circle-solid
                                    class="tag-icon"
                                  />
                                </template>
                                <span class="tag-value">
                                  {{
                                    formatBytes(
                                      hostInfo?.agent_metrics?.default_net_info
                                        ?.net_out_speed || 0
                                    )
                                  }}/s
                                </span>
                              </a-tag>
                            </div>
                            <div class="metric-item">
                              <a-tag color="green" class="network-tag">
                                <template #icon>
                                  <icon-streamline-download-circle-solid
                                    class="tag-icon"
                                  />
                                </template>
                                <span class="tag-value">
                                  {{
                                    formatBytes(
                                      hostInfo?.agent_metrics?.default_net_info
                                        ?.net_in_speed || 0
                                    )
                                  }}/s
                                </span>
                              </a-tag>
                            </div>
                          </div>
                        </div>
                      </a-col>

                      <!-- 总流量面板 -->
                      <a-col :span="12">
                        <div class="network-section">
                          <div class="section-title text-right">Total</div>
                          <div class="network-metrics">
                            <div class="metric-item">
                              <a-tag
                                color="blue"
                                class="network-tag justify-end"
                              >
                                <template #icon>
                                  <icon-mdi-summation class="tag-icon" />
                                </template>
                                <span class="tag-value">
                                  {{
                                    formatBytes(
                                      hostInfo?.agent_metrics?.default_net_info
                                        ?.net_out_transfer || 0
                                    )
                                  }}
                                </span>
                              </a-tag>
                            </div>
                            <div class="metric-item">
                              <a-tag
                                color="blue"
                                class="network-tag justify-end"
                              >
                                <template #icon>
                                  <icon-mdi-summation class="tag-icon" />
                                </template>
                                <span class="tag-value">
                                  {{
                                    formatBytes(
                                      hostInfo?.agent_metrics?.default_net_info
                                        ?.net_in_transfer || 0
                                    )
                                  }}
                                </span>
                              </a-tag>
                            </div>
                          </div>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card class="overview-card">
                  <div class="process-card">
                    <a-row :gutter="16">
                      <a-col :span="24">
                        <div class="process-section">
                          <div class="section-title">Process & Conn</div>
                          <div class="metrics-container">
                            <div class="metric-row">
                              <span class="label">TCP</span>
                              <span class="value">{{
                                hostInfo?.agent_metrics?.conn_info
                                  ?.tcp_conn_count || 0
                              }}</span>
                            </div>
                            <div class="metric-row">
                              <span class="label">UDP</span>
                              <span class="value">{{
                                hostInfo?.agent_metrics?.conn_info
                                  ?.udp_conn_count || 0
                              }}</span>
                            </div>
                            <div class="metric-row">
                              <span class="label">进程数</span>
                              <span class="value">{{
                                hostInfo?.agent_metrics?.conn_info
                                  ?.process_count || 0
                              }}</span>
                            </div>
                          </div>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px">
              <a-col :span="10">
                <a-card class="monitor-card">
                  <div class="chart-container">
                    <v-chart
                      :option="cpuChartOption"
                      autoresize
                      class="cpu-chart"
                      style="width: 100%; height: 100%"
                    />
                  </div>
                </a-card>
              </a-col>
              <a-col :span="14">
                <a-card class="monitor-card">
                  <div class="chart-container">
                    <v-chart
                      :option="networkChartOption"
                      autoresize
                      class="network-chart"
                      style="width: 100%; height: 100%"
                    />
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </template>
        <template
          v-if="position === 'diskAndNetwork' && hostInfo.agent_metrics"
        >
          <div class="detail-container">
            <a-row :gutter="16">
              <!-- 左侧磁盘信息 -->
              <a-col :span="12">
                <a-select
                  v-model="selectedDisk"
                  :style="{ width: '220px', marginBottom: '16px' }"
                  placeholder="选择挂载点"
                >
                  <a-option
                    v-for="disk in hostInfo?.agent_metrics?.partition_info"
                    :key="disk.mount_point"
                    :value="disk.mount_point"
                  >
                    {{ disk.mount_point }}
                  </a-option>
                </a-select>

                <a-card class="detail-card">
                  <div class="disk-info">
                    <!-- 挂载点名称 -->
                    <div class="mount-point">
                      <icon-clarity-hard-disk-solid class="disk-icon" />
                      <span class="mount-name">{{ selectedDisk }}</span>
                    </div>

                    <!-- 使用率进度条 -->
                    <div class="usage-section">
                      <div class="usage-title">Usage</div>
                      <div class="usage-progress">
                        <a-progress
                          :percent="(currentDiskInfo?.used_percent || 0) / 100"
                          :show-text="false"
                          :stroke-width="12"
                          :color="
                            (currentDiskInfo?.used_percent || 0) >= 90
                              ? 'rgb(var(--danger-6))'
                              : (currentDiskInfo?.used_percent || 0) >= 80
                                ? 'rgb(var(--warning-6))'
                                : ''
                          "
                        />
                        <div class="usage-percent">
                          {{ (currentDiskInfo?.used_percent || 0).toFixed(1) }}%
                        </div>
                      </div>
                    </div>

                    <!-- 详细信息 -->
                    <div class="disk-details">
                      <div class="detail-row total">
                        <div class="detail-label">Total</div>
                        <div class="detail-value">
                          {{ formatBytes(currentDiskInfo?.total || 0) }}
                        </div>
                        <div class="progress-bg"></div>
                      </div>
                      <div class="detail-row free">
                        <div class="detail-label">Free</div>
                        <div class="detail-value">
                          {{
                            formatBytes(
                              (currentDiskInfo?.total || 0) -
                                (currentDiskInfo?.used || 0)
                            )
                          }}
                        </div>
                        <div
                          class="progress-bg"
                          :style="{
                            width: `${100 - (currentDiskInfo?.used_percent || 0)}%`
                          }"
                        ></div>
                      </div>
                      <div class="detail-row used">
                        <div class="detail-label">Used</div>
                        <div class="detail-value">
                          {{ formatBytes(currentDiskInfo?.used || 0) }}
                        </div>
                        <div
                          class="progress-bg"
                          :style="{
                            width: `${currentDiskInfo?.used_percent || 0}%`
                          }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>

              <!-- 右侧网络信息 -->
              <a-col :span="12">
                <a-select
                  v-model="selectedInterface"
                  :style="{ width: '220px', marginBottom: '16px' }"
                  placeholder="选择网络接口"
                >
                  <a-option
                    v-for="net in hostInfo?.agent_metrics?.net_info"
                    :key="net.interface_name"
                    :value="net.interface_name"
                  >
                    {{ net.interface_name }}
                  </a-option>
                </a-select>

                <a-card class="detail-card">
                  <div class="network-info">
                    <!-- 网络接口标题 -->

                    <div class="interface-header">
                      <icon-ph-network-bold class="interface-icon" />
                      <span class="interface-name">{{
                        selectedInterface
                      }}</span>
                    </div>

                    <a-row :gutter="16">
                      <!-- 左侧图表 -->
                      <a-col :span="12">
                        <div class="chart-container">
                          <v-chart
                            class="network-chart"
                            :option="networkChartConfig"
                            :autoresize="true"
                          />
                        </div>
                      </a-col>

                      <!-- 右侧信息 -->
                      <a-col :span="12">
                        <div class="network-metrics">
                          <!-- 标题行 -->
                          <div class="metrics-header">
                            <span class="header-title">Asset</span>
                            <span class="header-title">Traffic</span>
                          </div>

                          <!-- 数据行 -->
                          <div class="metric-row">
                            <span class="label">Received</span>
                            <span class="value"
                              >{{
                                formatBytes(currentNetInfo?.net_in_speed || 0)
                              }}/s</span
                            >
                          </div>
                          <div class="metric-row">
                            <span class="label">Transmitted</span>
                            <span class="value"
                              >{{
                                formatBytes(currentNetInfo?.net_out_speed || 0)
                              }}/s</span
                            >
                          </div>
                          <div class="metric-row">
                            <span class="label">Total Received</span>
                            <span class="value">{{
                              formatBytes(currentNetInfo?.net_in_transfer || 0)
                            }}</span>
                          </div>
                          <div class="metric-row">
                            <span class="label">Total Transmitted</span>
                            <span class="value">{{
                              formatBytes(currentNetInfo?.net_out_transfer || 0)
                            }}</span>
                          </div>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </template>
      </template>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.host-title-container {
  padding-top: 20px;
  .back-button {
    color: var(--color-text-1);
    height: 30px;
    width: 30px;
  }
  .host-title {
    font-size: 17px;
    font-weight: 700;
    color: var(--color-text-1);
  }
}
.overview-container {
  margin-top: 40px;
}
.overview-card {
  height: 130px;
  border-radius: 10px;
  border: 1px solid var(--color-neutral-4);
  :deep(.arco-card-body) {
    color: var(--color-text-1);
    padding: 20px;
  }
}
.metric-card {
  display: flex;
  flex-direction: column;
  height: 100%;

  .title {
    font-size: 14px;
    font-weight: bold;
    color: var(--color-text-1);
    margin-bottom: 8px;
  }

  .title-total {
    font-size: 14px;
    font-weight: bold;
    color: var(--color-text-2);
    margin-bottom: 8px;
    text-align: right;
  }

  .main-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--color-text-1);
    margin-bottom: 8px;
  }

  .sub-info {
    font-size: 12px;
    font-weight: bold;
    color: var(--color-text-3);
  }
}
.memory-card {
  .chart-container {
    height: 100%;
    width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.vue-ui-donut) {
      width: 85px;
      height: 85px;
    }
  }
}
.network-panel {
  .network-section {
    .section-title {
      font-size: 14px;
      font-weight: bold;
      color: var(--color-text-1);
      margin-bottom: 8px;

      &.text-right {
        text-align: right;
        color: var(--color-text-2);
      }
    }

    .network-metrics {
      .metric-item {
        margin-bottom: 6px;
        display: flex;
        justify-content: flex-start;

        &:has(.justify-end) {
          justify-content: flex-end;
        }
      }

      .network-tag {
        width: 110px;
        border-radius: 10px;
        border: 1px solid var(--color-neutral-4);
        display: flex;
        align-items: center;

        &.justify-end {
          justify-content: flex-end;
        }
      }

      .tag-icon {
        padding-top: 8px;
      }

      .tag-value {
        margin-left: 6px;
        font-size: 14px;
      }
    }
  }
}
.process-card {
  height: 100%;

  .process-section {
    .section-title {
      font-size: 14px;
      font-weight: bold;
      color: var(--color-text-1);
      margin-bottom: 12px;
    }

    .metrics-container {
      .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 13px;
          font-weight: bold;
          color: var(--color-text-2);
        }

        .value {
          font-size: 13px;
          font-weight: 600;
          color: var(--color-text-1);
        }
      }
    }
  }
}
.detail-container {
  margin-top: 40px;

  .detail-card {
    min-height: 350px;
    border-radius: 10px;
    border: 1px solid var(--color-neutral-4);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: bold;
        color: var(--color-text-1);
      }
    }

    .disk-info,
    .network-info {
      padding: 16px 0;

      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .label {
          font-size: 14px;
          color: var(--color-text-2);
        }

        .value {
          font-size: 14px;
          font-weight: 500;
          color: var(--color-text-1);
        }
      }
    }

    .chart-container {
      height: 200px;
      margin-top: 20px;

      .network-chart {
        height: 200px;
        width: 140px;
      }
    }
  }
}
.detail-card {
  .disk-info {
    padding: 16px 0;

    .mount-point {
      display: flex;
      align-items: center;
      margin-bottom: 32px;
      padding: 0 16px;

      .disk-icon {
        font-size: 16px;
        margin-right: 12px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .mount-name {
        font-size: 16px;
        font-weight: 600;
        color: var(--color-text-1);
        line-height: 32px;
      }
    }

    .usage-section {
      margin-bottom: 24px;

      .usage-title {
        font-weight: bold;
        font-size: 14px;
        color: var(--color-text-2);
        margin-bottom: 8px;
      }

      .usage-progress {
        display: flex;
        align-items: center;
        gap: 12px;

        :deep(.arco-progress) {
          flex: 1;
        }

        .usage-percent {
          font-size: 14px;
          font-weight: 500;
          color: var(--color-text-1);
          min-width: 52px;
        }
      }
    }

    .disk-details {
      .detail-row {
        position: relative;
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 8px 12px;
        border-radius: 5px;
        // border: 1px solid var(--color-neutral-4);
        overflow: hidden;
        z-index: 1;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-size: 13px;
          color: var(--color-text-2);
          z-index: 2;
        }

        .detail-value {
          font-size: 13px;
          font-weight: 500;
          color: var(--color-text-1);
          z-index: 2;
        }

        .progress-bg {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          background-color: var(--color-fill-2);
          z-index: -1;
          transition: width 0.3s ease;
        }

        &.total .progress-bg {
          width: 100%;
        }

        &.free .progress-bg {
          background-color: var(--color-fill-2);
        }

        &.used .progress-bg {
          background-color: var(--color-fill-2);
        }
      }
    }
  }
}
.network-info {
  padding: 16px 0;

  .interface-header {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    padding: 0 16px;

    .interface-icon {
      font-size: 16px;
      margin-right: 12px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .interface-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-1);
      line-height: 32px;
    }
  }

  .chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;

    .network-chart {
      height: 200px;
      width: 140px;
    }
  }

  .network-metrics {
    padding: 0 16px;

    .metrics-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .header-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--color-text-2);
      }
    }

    .metric-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--color-border-1);

      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }

      .label {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-2);
        flex: 1;
      }

      .value {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-1);
        text-align: right;
        min-width: 100px;
      }
    }
  }
}

// 添加空状态样式
:deep(.arco-empty) {
  padding: 64px 0;
}

.host-info-container {
  padding: 20px;
}

.monitor-card {
  border-radius: 10px;
  border: 1px solid var(--color-neutral-4);

  .chart-container {
    height: 350px;
    width: 100%;
    :deep(.echarts) {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
