<script setup lang="ts">
import type { IpAddressRecord, IpAddressListParams } from '@/types/ipaddress'
import type { FieldRule, TableColumnData } from '@arco-design/web-vue'
import {
  reqGetIpAddressList,
  reqAddIpAddress,
  reqUpdateIpAddress,
  reqDeleteIpAddress
} from '@/api/ipaddress'

// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<IpAddressListParams>({
  page: 1,
  page_size: 10,
  internet_port: '', // 互联网端口搜索
  mapped_port: '' // 内网端口搜索
})

// 表格列配置
const columns = [
  {
    title: '互联网地址',
    dataIndex: 'internet_ip',
    width: 140,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '互联网端口',
    dataIndex: 'internet_port',
    width: 140,
    ellipsis: true,
    tooltip: true,
    render: ({ record }: { record: IpAddressRecord }) =>
      record.internet_port.join(', ')
  },
  {
    title: '内网地址',
    dataIndex: 'mapped_ip',
    width: 140,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '内网端口',
    dataIndex: 'mapped_port',
    width: 140,
    ellipsis: true,
    tooltip: true,
    render: ({ record }: { record: IpAddressRecord }) =>
      record.mapped_port.join(', ')
  },
  {
    title: '运营商',
    dataIndex: 'operator',
    width: 140,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 140,
    slotName: 'status'
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 200,
    fixed: 'right'
  }
]

// 加载状态
const loading = ref(false)
// 数据总数
const total = ref(0)
// IP地址列表数据
const ipAddressList = ref<IpAddressRecord[]>([])

// 分页配置
const pagination = computed(() => ({
  total: total.value,
  current: params.page,
  pageSize: params.page_size,
  showTotal: true,
  showPageSize: true
}))

// IP地址校验规则
const validateIP = (value: string) => {
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
  return ipRegex.test(value)
}

// 端口校验规则
const validatePorts = (value: string[]) => {
  if (!value.length) return true

  // 存储所有展开后的端口号
  const allPorts = new Set<number>()

  for (const portStr of value) {
    // 处理端口范围格式 (例如: 1801-1805)
    if (portStr.includes('-')) {
      const [start, end] = portStr.split('-').map(Number)

      // 验证范围格式
      if (
        !Number.isInteger(start) ||
        !Number.isInteger(end) ||
        start < 1 ||
        end > 65535 ||
        start > end
      ) {
        return false
      }

      // 添加范围内的所有端口
      for (let port = start; port <= end; port++) {
        if (allPorts.has(port)) return false // 检查重复
        allPorts.add(port)
      }
    } else {
      // 处理单个端口
      const port = Number(portStr)
      if (!Number.isInteger(port) || port < 1 || port > 65535) {
        return false
      }
      if (allPorts.has(port)) return false // 检查重复
      allPorts.add(port)
    }
  }

  return true
}

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  internet_ip: [
    { required: true, message: '请输入互联网地址' },
    {
      validator: (value, cb) => {
        if (!validateIP(value)) {
          cb('请输入正确的地址格式')
        }
        cb()
      }
    }
  ],
  mapped_ip: [
    { required: true, message: '请输入内网地址' },
    {
      validator: (value, cb) => {
        if (!validateIP(value)) {
          cb('请输入正确的地址格式')
        }
        cb()
      }
    }
  ],
  internet_port: [
    { required: true, message: '请输入互联网端口' },
    {
      validator: (value, cb) => {
        if (!validatePorts(value)) {
          if (value.some((port: string) => port.includes('-'))) {
            const hasInvalidRange = value.some((port: string) => {
              if (!port.includes('-')) return false
              const [start, end] = port.split('-').map(Number)
              return (
                !Number.isInteger(start) ||
                !Number.isInteger(end) ||
                start < 1 ||
                end > 65535 ||
                start > end
              )
            })
            if (hasInvalidRange) {
              cb('端口范围格式错误，正确格式如：1801-1805')
            } else {
              cb('端口不能重复，且必须在1-65535之间')
            }
          } else {
            cb('端口不能重复，且必须在1-65535之间')
          }
        }
        cb()
      }
    }
  ],
  mapped_port: [
    { required: true, message: '请输入内网端口' },
    {
      validator: (value, cb) => {
        if (!validatePorts(value)) {
          if (value.some((port: string) => port.includes('-'))) {
            const hasInvalidRange = value.some((port: string) => {
              if (!port.includes('-')) return false
              const [start, end] = port.split('-').map(Number)
              return (
                !Number.isInteger(start) ||
                !Number.isInteger(end) ||
                start < 1 ||
                end > 65535 ||
                start > end
              )
            })
            if (hasInvalidRange) {
              cb('端口范围格式错误，正确格式如：1801-1805')
            } else {
              cb('端口不能重复，且必须在1-65535之间')
            }
          } else {
            cb('端口不能重复，且必须在1-65535之间')
          }
        }
        cb()
      }
    }
  ],
  operator: [{ required: true, message: '请输入运营商' }],
  status: [{ required: true, message: '请选择状态' }]
}

// 获取IP地址列表
const getIpAddressList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const res = await reqGetIpAddressList(params)
    ipAddressList.value = res.data.ip_address
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取IP地址列表失败')
  } finally {
    loading.value = false
  }
}

// 模态框相关
const visible = ref(false)
const modalType = ref<'add' | 'edit'>('add')
const formRef = ref()
const ipAddressForm = reactive<IpAddressRecord>({
  internet_ip: '',
  internet_port: [],
  mapped_ip: '',
  mapped_port: [],
  operator: '',
  description: '',
  status: 'enable'
})

// 打开添加模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: IpAddressRecord) => {
  modalType.value = 'edit'
  Object.assign(ipAddressForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (loading.value) return
  try {
    loading.value = true
    modalType.value === 'add'
      ? await reqAddIpAddress(ipAddressForm)
      : await reqUpdateIpAddress(ipAddressForm)
    AMessage.success(
      modalType.value === 'add' ? '添加IP地址成功' : '编辑IP地址成功'
    )
    closeModal()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
    getIpAddressList()
  }
}

// 删除IP地址
const handleDelete = async (record: IpAddressRecord) => {
  if (loading.value) return
  try {
    loading.value = true
    await reqDeleteIpAddress(record.id as string)
    AMessage.success('删除IP地址成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除IP地址失败')
  } finally {
    loading.value = false
    getIpAddressList()
  }
}

// 分页处理
const onPageChange = (current: number) => {
  params.page = current
  getIpAddressList()
}

const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getIpAddressList()
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getIpAddressList()
}

// 组件挂载时获取列表
onMounted(() => {
  getIpAddressList()
})

// 在 script setup 中添加运营商配置
const operatorOptions = [
  { label: '中国电信', value: '中国电信' },
  { label: '中国移动', value: '中国移动' },
  { label: '中国联通', value: '中国联通' },
  { label: '其他', value: '其他' }
] as const

// 添加表单验证方法
const checkFormRule = async () => {
  try {
    const res = await formRef.value?.validate()
    return !res
  } catch (error) {
    return false
  }
}

// 在 script setup 中添加详情抽屉控制变量
const drawerVisible = ref(false)
const currentIpAddress = ref<IpAddressRecord | null>(null)

// 添加打开和关闭抽屉的方法
const openDrawer = (record: IpAddressRecord) => {
  currentIpAddress.value = { ...record }
  drawerVisible.value = true
}

const closeDrawer = () => {
  drawerVisible.value = false
  currentIpAddress.value = null
}
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="互联网地址映射管理">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="互联网地址" field="internet_ip">
                  <a-input
                    v-model="params.internet_ip"
                    placeholder="请输入互联网地址"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="互联网端口" field="internet_port">
                  <a-input
                    v-model="params.internet_port"
                    placeholder="请输入互联网端口"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="运营商" field="operator">
                  <a-select
                    v-model="params.operator"
                    placeholder="请选择运营商"
                    allow-clear
                    allow-search
                  >
                    <a-option
                      v-for="item in operatorOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="内网地址" field="mapped_ip">
                  <a-input
                    v-model="params.mapped_ip"
                    placeholder="请输入内网地址"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="内网端口" field="mapped_port">
                  <a-input
                    v-model="params.mapped_port"
                    placeholder="请输入内网端口"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态" field="status">
                  <a-select
                    v-model="params.status"
                    placeholder="请选择状态"
                    allow-clear
                  >
                    <a-option value="enable">启用</a-option>
                    <a-option value="disable">禁用</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="getIpAddressList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getIpAddressList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns as TableColumnData[]"
        :data="ipAddressList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #status="{ record }">
          <div class="status-wrapper">
            <div class="status-icon">
              <icon-grommet-icons-status-good-small
                :color="record.status === 'enable' ? '#00a245' : '#e00101'"
              />
            </div>
            <span>{{ record.status === 'enable' ? '启用' : '禁用' }}</span>
          </div>
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="mini" @click="openDrawer(record)">
              详情
            </a-button>
            <a-popconfirm
              content="确定要删除该IP地址吗？"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalType === 'add' ? '添加IP地址' : '编辑IP地址'"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="formRef"
          :model="ipAddressForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="互联网地址" field="internet_ip">
            <a-input
              v-model="ipAddressForm.internet_ip"
              placeholder="请输入互联网地址"
            />
          </a-form-item>
          <a-form-item label="互联网端口" field="internet_port">
            <a-input-tag
              v-model="ipAddressForm.internet_port"
              placeholder="输入单个端口或范围(如:1801-1805)后回车"
            />
          </a-form-item>
          <a-form-item label="内网地址" field="mapped_ip">
            <a-input
              v-model="ipAddressForm.mapped_ip"
              placeholder="请输入内网地址"
            />
          </a-form-item>
          <a-form-item label="内网端口" field="mapped_port">
            <a-input-tag
              v-model="ipAddressForm.mapped_port"
              placeholder="输入单个端口或范围(如:1801-1805)后回车"
            />
          </a-form-item>
          <a-form-item label="运营商" field="operator">
            <a-select
              v-model="ipAddressForm.operator"
              placeholder="请选择运营商"
              allow-search
            >
              <a-option
                v-for="item in operatorOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态" field="status">
            <a-select v-model="ipAddressForm.status" placeholder="请选择状态">
              <a-option value="enable">启用</a-option>
              <a-option value="disable">禁用</a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="描述" field="description">
            <a-textarea
              v-model="ipAddressForm.description"
              placeholder="请输入描述信息"
              :auto-size="{ minRows: 2, maxRows: 5 }"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 详情抽屉 -->
      <a-drawer
        :visible="drawerVisible"
        @cancel="closeDrawer"
        :width="400"
        :footer="false"
        unmountOnClose
      >
        <template #title>
          <a-space>
            <icon-tabler-network />
            IP地址详情
          </a-space>
        </template>
        <div class="drawer-content">
          <a-descriptions
            title="基本信息"
            size="small"
            :column="1"
            :data="[
              {
                label: '互联网地址',
                value: currentIpAddress?.internet_ip || '-'
              },
              {
                label: '互联网端口',
                value: currentIpAddress?.internet_port?.join(', ') || '-'
              },
              {
                label: '内网地址',
                value: currentIpAddress?.mapped_ip || '-'
              },
              {
                label: '内网端口',
                value: currentIpAddress?.mapped_port?.join(', ') || '-'
              },
              {
                label: '运营商',
                value: currentIpAddress?.operator || '-'
              },
              {
                label: '状态',
                value: currentIpAddress?.status === 'enable' ? '启用' : '禁用'
              }
            ]"
          />

          <a-descriptions
            title="其他信息"
            :column="1"
            :data="[
              {
                label: '描述信息',
                value: currentIpAddress?.description || '暂无描述'
              }
            ]"
            style="margin-top: 24px"
          />
        </div>
      </a-drawer>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.status-wrapper {
  display: flex;
  align-items: center;
}

.status-icon {
  display: flex;
  align-items: center;
  font-size: 5px;
  margin-right: 4px;
}

// 添加描述列表标题样式
:deep(.arco-descriptions-title) {
  font-size: 14px;
  color: var(--color-neutral-8);
  font-weight: bold;
  margin-bottom: 8px;
}

// 调整标签和内容的字体大小
:deep(.arco-descriptions-item-label),
:deep(.arco-descriptions-item-value) {
  font-size: 13px;
  font-weight: 500;
}
</style>
