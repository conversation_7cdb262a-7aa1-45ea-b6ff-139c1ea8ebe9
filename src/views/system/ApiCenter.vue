<script setup lang="ts">
import {
  reqGetApiList,
  reqAddApi,
  reqDeleteApi,
  reqUpdateApi,
  reqGetApiGroups
} from '@/api/api'
import type { ApiRecord, ApiListParams } from '@/types/api'
import { ref, reactive, computed, onMounted } from 'vue'
import type { FieldRule } from '@arco-design/web-vue'

// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<ApiListParams>({
  page: 1,
  page_size: 10
})

// 表格列配置
const columns = [
  {
    title: '接口名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '接口路径',
    dataIndex: 'path',
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '请求方法',
    dataIndex: 'method',
    width: 150,
    slotName: 'method'
  },
  {
    title: '接口分组',
    dataIndex: 'group',
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
    tooltip: true
  },
  { title: '操作', dataIndex: 'operations', slotName: 'operations', width: 150 }
]

// 加载状态标识
const loading = ref(false)

// 数据总条数
const total = ref(0)
// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// API列表数据
const apiList = ref<ApiRecord[]>([])

// 获取API列表数据
const getApiList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const res = await reqGetApiList(params)
    apiList.value = res.data.apis
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取API列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const onPageChange = (current: number) => {
  params.page = current
  getApiList()
}

// 每页条数变化处理
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getApiList()
}

// 模态框显示状态
const visible = ref(false)
// 模态框操作类型
const modalType = ref<'add' | 'edit'>('add')
// API表单数据
const apiForm = reactive<ApiRecord>({
  name: '',
  group: '',
  method: '',
  path: '',
  description: ''
})
// 表单实例
const apiFormRef = ref()

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  name: [{ required: true, message: '请输入接口名称' }],
  path: [
    { required: true, message: '请输入接口路径' },
    {
      match: /^\//, // 以/开头
      message: '接口路径必须以/开头'
    }
  ],
  method: [{ required: true, message: '请选择请求方法' }],
  group: [{ required: true, message: '请输入接口分组' }]
}

// 计算模态框标题
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加接口' : '编辑接口'
)

// 打开添加模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: ApiRecord) => {
  modalType.value = 'edit'
  Object.assign(apiForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  apiFormRef.value?.resetFields()
}

// 表单提交前校验
const checkFormRule = async () => {
  const res = await apiFormRef.value.validate()
  return !res
}

// 提交表单
const handleSubmit = async () => {
  if (loading.value) return
  try {
    loading.value = true
    modalType.value === 'add'
      ? await reqAddApi(apiForm)
      : await reqUpdateApi(apiForm)
    AMessage.success(
      modalType.value === 'add' ? '添加接口成功' : '编辑接口成功'
    )
    closeModal()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
    getApiList()
    getApiGroups()
  }
}

// 删除接口
const handleDelete = async (record: ApiRecord) => {
  if (loading.value) return
  try {
    loading.value = true
    await reqDeleteApi(record.id as string)
    AMessage.success('删除接口成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除接口失败')
  } finally {
    loading.value = false
    getApiList()
    getApiGroups()
  }
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getApiList()
}

// 组件挂载时获取列表
onMounted(() => {
  getApiList()
  getApiGroups()
})

// 在 script setup 中添加
const httpMethods = [
  'GET',
  'POST',
  'PUT',
  'DELETE',
  'PATCH',
  'HEAD',
  'OPTIONS',
  'TRACE',
  'CONNECT'
]

// 接口分组列表
const groupList = ref<string[]>([])

// 获取接口分组列表
const getApiGroups = async () => {
  try {
    const res = await reqGetApiGroups()
    groupList.value = res.data.groups
  } catch (error: any) {
    AMessage.error(error.message || '获取接口分组列表失败')
  }
}

// 在 script setup 中添加
const methodColorMap = {
  GET: 'blue',
  POST: 'green',
  PUT: 'orange',
  DELETE: 'red',
  PATCH: 'purple',
  HEAD: 'gray',
  OPTIONS: 'cyan',
  TRACE: 'arcoblue',
  CONNECT: 'magenta'
} as const
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="接口中心">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="接口名称" field="name">
                  <a-input v-model="params.name" placeholder="请输入接口名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="接口路径" field="path">
                  <a-input v-model="params.path" placeholder="请输入接口路径" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="接口分组" field="group">
                  <a-select
                    v-model="params.group"
                    placeholder="请选择接口分组"
                    allow-search
                  >
                    <a-option
                      v-for="group in groupList"
                      :key="group"
                      :value="group"
                    >
                      {{ group }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="请求方法" field="method">
                  <a-select
                    v-model="params.method"
                    placeholder="请选择请求方法"
                  >
                    <a-option
                      v-for="method in httpMethods"
                      :key="method"
                      :value="method"
                    >
                      {{ method }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="getApiList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getApiList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="apiList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-popconfirm
              :content="`确定要删除${record.name}API吗？`"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
        <template #method="{ record }">
          <a-tag
            :color="
              methodColorMap[record.method as keyof typeof methodColorMap]
            "
          >
            {{ record.method }}
          </a-tag>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="apiFormRef"
          :model="apiForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="接口名称" field="name">
            <a-input v-model="apiForm.name" placeholder="请输入接口名称" />
          </a-form-item>
          <a-form-item label="接口路径" field="path">
            <a-input
              v-model="apiForm.path"
              placeholder="请输入接口路径，以/开头"
            />
          </a-form-item>
          <a-form-item label="请求方法" field="method">
            <a-select v-model="apiForm.method" placeholder="请选择请求方法">
              <a-option
                v-for="method in httpMethods"
                :key="method"
                :value="method"
              >
                {{ method }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="接口分组" field="group">
            <a-select
              v-model="apiForm.group"
              placeholder="请输入或选择接口分组"
              allow-create
              allow-search
            >
              <a-option v-for="group in groupList" :key="group" :value="group">
                {{ group }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="描述" field="description">
            <a-textarea
              v-model="apiForm.description"
              placeholder="请输入接口描述"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}
</style>
