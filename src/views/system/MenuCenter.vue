<script setup lang="ts">
import type { MenuRecord, MenuListParams } from '@/types/menu'
import {
  reqGetMenuList,
  reqAddMenu,
  reqUpdateMenu,
  reqDeleteMenu
} from '@/api/menu'
import type { FieldRule } from '@arco-design/web-vue'

// 查询表单引用
const searchForm = ref()

// 查询参数
const params = reactive<MenuListParams>({
  page: 1,
  page_size: 10,
  name: '',
  identifier: '',
  type: undefined
})

// 菜单类型选项
const typeOptions = [
  { label: '菜单', value: 'menu' },
  { label: '按钮', value: 'button' }
]

// 表格列配置
const columns = [
  { title: '菜单名称', dataIndex: 'name' },
  { title: '资源标识', dataIndex: 'identifier' },
  { title: '类型', dataIndex: 'type', slotName: 'type' },
  { title: '操作', dataIndex: 'operations', slotName: 'operations', width: 300 }
]

// 加载状态
const loading = ref(false)
const total = ref(0)

// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 菜单树数据
const menuList = ref<MenuRecord[]>([])

// 获取菜单列表
const getMenuList = async () => {
  try {
    loading.value = true
    const res = await reqGetMenuList(params)
    menuList.value = res.data.menus
    total.value = res.data.total
  } catch (error: any) {
    // 这里不需要处理 401 错误，因为已经在请求拦截器中处理了
    AMessage.error(error.message || '获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

// 页码改变回调
const onPageChange = (current: number) => {
  params.page = current
  getMenuList()
}

// 每页条数改变回调
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getMenuList()
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  params.page = 1
  getMenuList()
}

// 模态框相关
const visible = ref(false)
const modalType = ref<'add' | 'edit'>('add')
const menuForm = reactive<MenuRecord>({
  id: '',
  name: '',
  identifier: '',
  type: 'menu',
  parent_id: null
})
const menuFormRef = ref()

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  name: [{ required: true, message: '请输入菜单名称' }],
  identifier: [{ required: true, message: '请输入资源标识' }],
  type: [{ required: true, message: '请选择类型' }]
}

// 模态框标题
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加菜单' : '编辑菜单'
)

// 打开添加模态框
const openAddModal = (parentId: string | null = null) => {
  modalType.value = 'add'
  // 重置表单
  menuForm.parent_id = parentId
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: MenuRecord) => {
  modalType.value = 'edit'
  // 使用对象解构赋值来设置表单数据
  Object.assign(menuForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  // 重置表单为初始状态
  menuFormRef.value?.resetFields()
}

// 表单提交前校验
const checkFormRule = async () => {
  // 顶级菜单必须是菜单类型
  if (!menuForm.parent_id && menuForm.type !== 'menu') {
    AMessage.error('顶级菜单必须是菜单类型')
    return false
  }
  const res = await menuFormRef.value.validate()
  return !res
}

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true
    modalType.value === 'add'
      ? await reqAddMenu(menuForm)
      : await reqUpdateMenu(menuForm)

    AMessage.success(
      modalType.value === 'add' ? '添加菜单成功' : '编辑菜单成功'
    )
    closeModal()
    getMenuList()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

// 删除菜单
const handleDelete = async (record: MenuRecord) => {
  // 如果有子菜单，不允许删除
  if (record.children?.length) {
    AMessage.error('该菜单下有子菜单，不能删除')
    return
  }

  try {
    loading.value = true
    await reqDeleteMenu(record.id as string)
    AMessage.success('删除菜单成功')
    getMenuList()
  } catch (error: any) {
    AMessage.error(error.message || '删除菜单失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取表
onMounted(() => {
  getMenuList()
})
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="菜单中心">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="菜单名称" field="name">
                  <a-input v-model="params.name" placeholder="请输入菜单名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资源标识" field="identifier">
                  <a-input
                    v-model="params.identifier"
                    placeholder="请输入资源标识"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="类型" field="type">
                  <a-select
                    v-model="params.type"
                    placeholder="请选择类型"
                    allow-clear
                  >
                    <a-option
                      v-for="option in typeOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 35px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="getMenuList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal()">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getMenuList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="menuList"
        :loading="loading"
        :bordered="false"
        row-key="id"
        :pagination="pagination"
        :tree-props="{
          children: 'children'
        }"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #type="{ record }">
          <a-tag :color="record.type === 'menu' ? 'blue' : 'green'">
            {{ record.type === 'menu' ? '菜单' : '按钮' }}
          </a-tag>
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button
              v-if="record.type === 'menu'"
              type="text"
              size="mini"
              @click="openAddModal(record.id as string)"
            >
              添加子项
            </a-button>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-popconfirm
              :content="`确定要删除${record.name}吗？`"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="menuFormRef"
          :model="menuForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="菜单名称" field="name">
            <a-input v-model="menuForm.name" placeholder="请输入菜单名称" />
          </a-form-item>
          <a-form-item label="资源标识" field="identifier">
            <a-input
              v-model="menuForm.identifier"
              placeholder="请输入资源标识"
            />
          </a-form-item>
          <a-form-item label="类型" field="type">
            <a-select
              v-model="menuForm.type"
              placeholder="请选择类型"
              :disabled="!menuForm.parent_id"
            >
              <a-option
                v-for="option in typeOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}
</style>
