<script setup lang="ts">
import {
  reqGetUserList,
  reqAddUser,
  reqUpdateUser,
  reqDeleteUser,
  reqResetPassword
} from '@/api/user'
import type { UserListParams, UserRecord } from '@/types/user'
import type { FieldRule, TableColumnData } from '@arco-design/web-vue'
import { reqGetGroupList } from '@/api/group'
import type { GroupRecord } from '@/types/group'
import { reqGetRoleList } from '@/api/role'
import type { RoleRecord } from '@/types/role'

const params: UserListParams = reactive({
  page: 1,
  page_size: 10
})

const searchForm = ref()
const userListData = ref<UserRecord[]>([])
const loading = ref(false)
const total = ref(0)
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

const userFormRef = ref()
const userFormData = ref<UserRecord>({
  username: '',
  loginname: '',
  auth_mode: 'local',
  status: 'enable'
})

const visible = ref(false)
// 添加模态框状态（新增/编辑）
const modalType = ref<'add' | 'edit'>('add')

// 修改模态框标题计算属性
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加用户' : '编辑用户'
)

// 关闭模态框并重置表单
const closeModal = () => {
  visible.value = false
  userFormRef.value?.resetFields()
}

// 打开添加用户模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑用户模态框
const openEditModal = (record: UserRecord) => {
  modalType.value = 'edit'
  visible.value = true
  userFormData.value = { ...record }
}

// 将表单校验规则提取为独立的常量
const formRules: Record<string, FieldRule | FieldRule[]> = {
  username: [{ required: true, message: '请输入用户名' }],
  loginname: [
    { required: true, message: '请输入登录名' },
    {
      match: /^[a-zA-Z0-9_]+$/,
      message: '登录名只能包含英文字母、数字和下划线'
    }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { minLength: 6, message: '密码长度不能小于6位' }
  ],
  email: [{ type: 'email', message: '请输入正确的邮箱地址' }],
  phone: [
    {
      match: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号'
    }
  ],
  idcard: [
    {
      match: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: '请输入正确的身份证号'
    }
  ]
}

// 表格列配置
const columns = reactive<TableColumnData[]>([
  { title: '用户名', dataIndex: 'username', ellipsis: true, tooltip: true },
  { title: '登录名', dataIndex: 'loginname', ellipsis: true, tooltip: true },
  {
    title: '角色',
    dataIndex: 'role_names',
    ellipsis: true,
    tooltip: true,
    render: ({ record }) => getRoleNames(record.roles)
  },
  {
    title: '所属组',
    dataIndex: 'group_names',
    ellipsis: true,
    tooltip: true,
    render: ({ record }) => getGroupNames(record.groups)
  },
  {
    title: '认证方式',
    dataIndex: 'auth_mode',
    slotName: 'auth_mode',
    width: 100
  },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  {
    title: '手机号',
    dataIndex: 'phone',
    ellipsis: true,
    tooltip: true,
    width: 150
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  { title: '身份证号', dataIndex: 'idcard', width: 180 },
  { title: '创建时间', dataIndex: 'created_time', width: 180 },
  { title: '最后登录时间', dataIndex: 'last_login_time', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    fixed: 'right',
    width: 250
  }
])
// 页码改变回调
const onChange = (current: number) => {
  params.page = current
  getUserList()
}

// 每页条数改变回调
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getUserList()
}

const getUserList = async () => {
  try {
    loading.value = true
    const res = await reqGetUserList(params)
    userListData.value = res.data.users
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const reset = () => {
  searchForm.value.resetFields()
  getUserList()
}

// 校验用户表单
const checkFormRule = async () => {
  const res = await userFormRef.value.validate()
  return !res
}

// 提交用户表单（新增/编辑）
const SubmitUserModal = async () => {
  try {
    loading.value = true
    // 根据模态框状态调用不同的 API
    const res =
      modalType.value === 'add'
        ? await reqAddUser(userFormData.value)
        : await reqUpdateUser(userFormData.value)

    AMessage.success(
      res.message ||
        (modalType.value === 'add' ? '添加用户成功' : '编辑用户成功')
    )
    closeModal()
    getUserList()
  } catch (error: any) {
    AMessage.error(error.message)
  } finally {
    loading.value = false
  }
}

// 优化删除用户的错误处理
const deleteUser = async (id: string) => {
  if (loading.value) return // 防止重复点击

  try {
    loading.value = true
    const res = await reqDeleteUser(id)
    AMessage.success(res.message)
    await getUserList()
  } catch (error: any) {
    AMessage.error(error.message || '删除用户失败')
  } finally {
    loading.value = false
  }
}

// 启用/禁用用户
const enableUser = async (record: UserRecord) => {
  try {
    loading.value = true
    const res = await reqUpdateUser({
      ...record,
      status: record.status === 'enable' ? 'disable' : 'enable'
    })
    AMessage.success(
      record.status === 'enable' ? '启用用户成功' : '禁用用户成功'
    )
    getUserList()
  } catch (error: any) {
    AMessage.error('操作用户状态失败')
  } finally {
    loading.value = false
  }
}

const resetPasswordVisible = ref(false)
const resetPasswordForm = reactive({
  password: '',
  id: ''
})
const resetPasswordFormRef = ref()

// 优化重置密码相关函数
const resetPasswordCancel = () => {
  resetPasswordVisible.value = false
  resetPasswordFormRef.value?.resetFields()
  resetPasswordFormRef.value?.clearValidate()
}

// 添加表单校验函数
const checkResetPasswordForm = async () => {
  const valid = await resetPasswordFormRef.value.validate()
  return !valid
}

// 修改重置密码的处理函数
const handleResetPassword = (record: UserRecord) => {
  resetPasswordForm.id = record.id as string
  resetPasswordVisible.value = true
}
// 优化确认重置密码函数
const confirmResetPassword = async () => {
  if (loading.value) return

  try {
    loading.value = true
    await reqResetPassword(resetPasswordForm.id, resetPasswordForm.password)
    AMessage.success('重置密码成功')
    resetPasswordVisible.value = false
  } catch (error: any) {
    AMessage.error(error.message || '重置密码失败')
  } finally {
    loading.value = false
    resetPasswordCancel()
  }
}

// 添加组列表数据存储
const groupListOptions = ref<GroupRecord[]>([])

// 添加获取组列表的方法
const getGroupList = async () => {
  try {
    const res = await reqGetGroupList({ page: 1, page_size: 999 })
    groupListOptions.value = res.data.groups
  } catch (error: any) {
    console.error('获取组列表失败:', error)
  }
}

// 添加组名称映射方法
const getGroupNames = (groupIds: string[]) => {
  if (!groupIds?.length) return ''
  return groupIds
    .map(id => groupListOptions.value.find(group => group.id === id)?.name)
    .filter(Boolean)
    .join(', ')
}

// 添加角色列表数据存储
const roleListOptions = ref<RoleRecord[]>([])

// 添加获取角色列表的方法
const getRoleList = async () => {
  try {
    const res = await reqGetRoleList({ page: 1, page_size: 999 })
    roleListOptions.value = res.data.roles
  } catch (error: any) {
    console.error('获取角色列表失败:', error)
  }
}

// 添加角色名称映射方法
const getRoleNames = (roleIds: string[]) => {
  if (!roleIds?.length) return ''
  return roleIds
    .map(id => roleListOptions.value.find(role => role.id === id)?.name)
    .filter(Boolean)
    .join(', ')
}

onMounted(() => {
  getUserList()
  getGroupList()
  getRoleList()
})
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="用户中心">
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="用户名" field="username">
                  <a-input
                    v-model="params.username"
                    placeholder="请输入用户用户名"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="登录名" field="loginname">
                  <a-input
                    v-model="params.loginname"
                    placeholder="请输入用户登录名"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="用户组" field="groups">
                  <a-select
                    v-model="params.groups"
                    placeholder="请选择用户组"
                    allow-clear
                  >
                    <a-option
                      v-for="group in groupListOptions"
                      :key="group.id"
                      :value="group.id"
                    >
                      {{ group.name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="角色" field="roles">
                  <a-select
                    v-model="params.roles"
                    placeholder="请选择角色"
                    allow-clear
                  >
                    <a-option
                      v-for="role in roleListOptions"
                      :key="role.id"
                      :value="role.id"
                    >
                      {{ role.name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="认证方式" field="auth_mode">
                  <a-select
                    v-model="params.auth_mode"
                    placeholder="请选择认证方式"
                    allow-clear
                  >
                    <a-option value="local">本地认证</a-option>
                    <a-option value="ldap"> LDAP </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="用户状态" field="status">
                  <a-select
                    v-model="params.status"
                    placeholder="请选择用户状态"
                    allow-clear
                  >
                    <a-option value="enable">启用</a-option>
                    <a-option value="disable">禁用</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="getUserList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-button>
            <template #icon>
              <icon-material-symbols-download />
            </template>
            下载
          </a-button>

          <a-tooltip content="刷新">
            <div class="action-icon" @click="getUserList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :data="userListData"
        :bordered="false"
        :scroll="{ x: '2000px' }"
        :pagination="pagination"
        :loading="loading"
        @page-change="onChange"
        @page-size-change="onPageSizeChange"
      >
        <template #status="{ record }">
          <div class="status-wrapper">
            <div class="status-icon">
              <icon-grommet-icons-status-good-small
                :color="record.status === 'enable' ? '#00a245' : '#e00101'"
              />
            </div>
            <span v-if="record.status === 'enable'">启用</span>
            <span v-else-if="record.status === 'disable'">禁用</span>
            <span v-else>未知</span>
          </div>
        </template>
        <template #auth_mode="{ record }">
          <span v-if="record.auth_mode === 'local'">本地认证</span>
          <span v-else-if="record.auth_mode === 'ldap'">LDAP</span>
          <span v-else>未知</span>
        </template>
        <template #action="{ record }">
          <a-button
            type="text"
            size="mini"
            @click="enableUser(record)"
            :disabled="record.loginname === 'admin'"
          >
            {{ record.status === 'disable' ? '启用' : '禁用' }}
          </a-button>
          <a-button
            type="text"
            :disabled="record.loginname === 'admin'"
            size="mini"
            @click="openEditModal(record)"
          >
            编辑
          </a-button>
          <a-button
            type="text"
            :disabled="
              record.auth_mode === 'ldap' || record.loginname === 'admin'
            "
            size="mini"
            @click="handleResetPassword(record)"
          >
            重置密码
          </a-button>
          <a-popconfirm
            :content="'确定要删除用户 ' + record.username + ' 吗？'"
            type="warning"
            @ok="deleteUser(record.id)"
          >
            <a-button
              type="text"
              size="mini"
              status="danger"
              :disabled="record.loginname === 'admin'"
            >
              删除
            </a-button>
          </a-popconfirm>
        </template>
      </a-table>

      <!-- 添加用户/编辑用户模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="SubmitUserModal"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="userFormRef"
          :model="userFormData"
          :rules="formRules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
          size="small"
        >
          <a-form-item label="用户名" field="username" validate-trigger="blur">
            <a-input
              :disabled="
                modalType === 'edit' && userFormData.auth_mode === 'ldap'
              "
              v-model="userFormData.username"
              placeholder="请输入用户名"
            />
          </a-form-item>

          <a-form-item label="登录名" field="loginname" validate-trigger="blur">
            <a-input
              :disabled="modalType === 'edit'"
              v-model="userFormData.loginname"
              placeholder="请输入登录名"
            />
          </a-form-item>

          <a-form-item label="手机号" field="phone">
            <a-input v-model="userFormData.phone" placeholder="请输入手机号" />
          </a-form-item>

          <a-form-item label="邮箱" field="email">
            <a-input v-model="userFormData.email" placeholder="请输入邮箱" />
          </a-form-item>

          <a-form-item label="身份证号" field="idcard">
            <a-input
              v-model="userFormData.idcard"
              placeholder="请输入身份证号"
            />
          </a-form-item>

          <a-form-item label="用户组" field="groups">
            <a-select
              v-model="userFormData.groups"
              placeholder="请选择用户组"
              multiple
            >
              <a-option
                v-for="group in groupListOptions"
                :key="group.id"
                :value="group.id"
              >
                {{ group.name }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="角色" field="roles">
            <a-select
              v-model="userFormData.roles"
              placeholder="请选择角色"
              multiple
            >
              <a-option
                v-for="role in roleListOptions"
                :key="role.id"
                :value="role.id"
              >
                {{ role.name }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item
            v-if="modalType === 'add'"
            label="认证方式"
            field="auth_mode"
          >
            <a-select
              v-model="userFormData.auth_mode"
              placeholder="请选择认证方式"
            >
              <a-option value="local">本地认证</a-option>
              <a-option value="ldap">LDAP</a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态" field="status">
            <a-radio-group v-model="userFormData.status">
              <a-radio value="enable">启用</a-radio>
              <a-radio value="disable">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="modalType === 'add'" label="密码" field="password">
            <a-input-password
              v-model="userFormData.password"
              placeholder="请输入密码"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 重置密码模态框 -->
      <a-modal
        v-model:visible="resetPasswordVisible"
        title="重置密码"
        @ok="confirmResetPassword"
        @cancel="resetPasswordCancel"
        :on-before-ok="checkResetPasswordForm"
      >
        <a-form
          ref="resetPasswordFormRef"
          :model="resetPasswordForm"
          :rules="{ password: formRules.password }"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
          size="small"
        >
          <a-form-item field="password" label="新密码">
            <a-input-password
              v-model="resetPasswordForm.password"
              placeholder="请输入新密码"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.status-wrapper {
  display: flex;
  align-items: center;
}

.status-icon {
  display: flex;
  align-items: center;
  font-size: 5px;
  margin-right: 4px;
}
</style>
