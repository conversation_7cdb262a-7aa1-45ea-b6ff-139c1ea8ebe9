<script setup lang="ts">
import type {
  AnnouncementRecord,
  AnnouncementListParams
} from '@/types/announcement'
import {
  reqGetAnnouncementList,
  reqAddAnnouncement,
  reqDeleteAnnouncement,
  reqUpdateAnnouncement,
  reqUpdateAnnouncementStatus
} from '@/api/announcement'
import type { FieldRule } from '@arco-design/web-vue'

// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<AnnouncementListParams>({
  page: 1,
  page_size: 10
})

// 表格列配置
const columns = [
  {
    title: '公告内容',
    dataIndex: 'content',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '开始时间',
    dataIndex: 'start_time',
    width: 180
  },
  {
    title: '结束时间',
    dataIndex: 'end_time',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 200
  }
]

// 加载状态标识
const loading = ref(false)

// 数据总条数
const total = ref(0)
// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 公告列表数据
const announcementList = ref<AnnouncementRecord[]>([])

// 获取公告列表数据
const getAnnouncementList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const res = await reqGetAnnouncementList(params)
    announcementList.value = res.data.announcements
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const onPageChange = (current: number) => {
  params.page = current
  getAnnouncementList()
}

// 每页条数变化处理
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getAnnouncementList()
}

// 模态框显示状态
const visible = ref(false)
// 模态框操作类型
const modalType = ref<'add' | 'edit'>('add')
// 公告表单数据
const announcementForm = reactive<AnnouncementRecord>({
  content: '',
  start_time: '',
  end_time: '',
  status: 'disable'
})
// 表单实例
const announcementFormRef = ref()

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  content: [{ required: true, message: '请输入公告内容' }],
  start_time: [{ required: true, message: '请选择开始时间' }],
  end_time: [{ required: true, message: '请选择结束时间' }]
}

// 计算模态框标题
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加公告' : '编辑公告'
)

// 打开添加模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: AnnouncementRecord) => {
  modalType.value = 'edit'
  Object.assign(announcementForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  announcementFormRef.value?.resetFields()
}

// 表单提交前校验
const checkFormRule = async () => {
  const res = await announcementFormRef.value.validate()
  return !res
}

// 提交表单
const handleSubmit = async () => {
  if (loading.value) return
  try {
    loading.value = true
    modalType.value === 'add'
      ? await reqAddAnnouncement(announcementForm)
      : await reqUpdateAnnouncement(announcementForm)
    AMessage.success(
      modalType.value === 'add' ? '添加公告成功' : '编辑公告成功'
    )
    closeModal()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
    getAnnouncementList()
  }
}

// 删除公告
const handleDelete = async (record: AnnouncementRecord) => {
  if (loading.value) return
  try {
    loading.value = true
    await reqDeleteAnnouncement(record.id as string)
    AMessage.success('删除公告成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除公告失败')
  } finally {
    loading.value = false
    getAnnouncementList()
  }
}

// 启用/禁用公告
const handleStatusChange = async (record: AnnouncementRecord) => {
  try {
    loading.value = true
    const newStatus = record.status === 'enable' ? 'disable' : 'enable'
    await reqUpdateAnnouncementStatus(record.id as string, newStatus)
    AMessage.success(`${newStatus === 'enable' ? '启用' : '禁用'}公告成功`)
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
    getAnnouncementList()
  }
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getAnnouncementList()
}

// 组件挂载时获取列表
onMounted(() => {
  getAnnouncementList()
})
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="公告中心">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="公告内容" field="content">
                  <a-input
                    v-model="params.content"
                    placeholder="请输入公告内容"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态" field="status">
                  <a-select
                    v-model="params.status"
                    placeholder="请选择状态"
                    allow-clear
                  >
                    <a-option value="enable">启用</a-option>
                    <a-option value="disable">禁用</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="开始时间" field="start_time">
                  <a-date-picker
                    v-model="params.start_time"
                    placeholder="请选择开始时间"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="结束时间" field="end_time">
                  <a-date-picker
                    v-model="params.end_time"
                    placeholder="请选择结束时间"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 35px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="getAnnouncementList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getAnnouncementList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="announcementList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #status="{ record }">
          <a-tag :color="record.status === 'enable' ? 'green' : 'red'">
            {{ record.status === 'enable' ? '启用' : '禁用' }}
          </a-tag>
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button
              type="text"
              size="mini"
              :status="record.status === 'enable' ? 'danger' : 'success'"
              @click="handleStatusChange(record)"
            >
              {{ record.status === 'enable' ? '禁用' : '启用' }}
            </a-button>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-popconfirm
              :content="`确定要删除该公告吗？`"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="announcementFormRef"
          :model="announcementForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="公告内容" field="content">
            <a-textarea
              v-model="announcementForm.content"
              placeholder="请输入公告内容"
            />
          </a-form-item>
          <a-form-item label="开始时间" field="start_time">
            <a-date-picker
              v-model="announcementForm.start_time"
              style="width: 100%"
              show-time
              placeholder="请选择开始时间"
            />
          </a-form-item>
          <a-form-item label="结束时间" field="end_time">
            <a-date-picker
              v-model="announcementForm.end_time"
              style="width: 100%"
              show-time
              placeholder="请选择结束时间"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}
</style>
