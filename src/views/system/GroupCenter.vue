<script setup lang="ts">
import type { GroupRecord, GroupListParams } from '@/types/group'
import {
  reqGetGroupList,
  reqAddGroup,
  reqDeleteGroup,
  reqUpdateGroup,
  reqUpdateGroupUsers,
  reqUpdateGroupHosts
} from '@/api/group'
import { reqGetUserList } from '@/api/user'
import { reqGetHostsList } from '@/api/hosts'
import type { FieldRule } from '@arco-design/web-vue'

// 查询表单实例
const searchForm = ref()

// 查询参数
const params = reactive<GroupListParams>({
  page: 1,
  page_size: 10
})

// 表格列配置
const columns = [
  {
    title: '组名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '组代码',
    dataIndex: 'code',
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '用户数量',
    dataIndex: 'users_count',
    slotName: 'users',
    width: 100
  },
  {
    title: '主机数量',
    dataIndex: 'hosts_count',
    slotName: 'hosts',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
    tooltip: true
  },
  { title: '操作', dataIndex: 'operations', slotName: 'operations', width: 250 }
]

// 加载状态标识
const loading = ref(false)

// 数据总条数
const total = ref(0)
// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 组列表数据
const groupList = ref<GroupRecord[]>([])

// 获取组列表数据
const getGroupList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const res = await reqGetGroupList(params)
    groupList.value = res.data.groups
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取组列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const onPageChange = (current: number) => {
  params.page = current
  getGroupList()
}

// 每页条数变化处理
const onPageSizeChange = (size: number) => {
  params.page_size = size
  params.page = 1
  getGroupList()
}

// 模态框显示状态
const visible = ref(false)
// 模态框操作类型
const modalType = ref<'add' | 'edit'>('add')
// 组表单数据
const groupForm = reactive<GroupRecord>({
  name: '',
  code: ''
})
// 表单实例
const groupFormRef = ref()

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  name: [{ required: true, message: '请输入组名称' }],
  code: [
    { required: true, message: '请输入组代码' },
    {
      match: /^[a-zA-Z0-9_]+$/,
      message: '组代码只能包含英文字母、数字和下划线'
    }
  ]
}

// 用户关联模态框显示状态
const userVisible = ref(false)
// 当前操作的组ID
const currentGroupId = ref('')
// 用户数据加载状态
const userLoading = ref(false)

// 穿梭框源数据
const transferData = ref<any[]>([])
// 已选用户ID列表
const targetKeys = ref<string[]>([])

// 主机关联模态框显示状态
const hostVisible = ref(false)
// 主机数据加载状态
const hostLoading = ref(false)
// 主机穿梭框数据
const hostTransferData = ref<any[]>([])
// 已选主机ID列表
const hostTargetKeys = ref<string[]>([])

// 计算模态框标题
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加组' : '编辑组'
)

// 打开添加模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: GroupRecord) => {
  modalType.value = 'edit'
  Object.assign(groupForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  groupFormRef.value?.resetFields()
}

// 表单提交前校验
const checkFormRule = async () => {
  const res = await groupFormRef.value.validate()
  return !res
}

// 提交表单
const handleSubmit = async () => {
  if (loading.value) return
  try {
    loading.value = true
    modalType.value === 'add'
      ? await reqAddGroup(groupForm)
      : await reqUpdateGroup(groupForm)
    AMessage.success(modalType.value === 'add' ? '添加组成功' : '编辑组成功')
    closeModal()
  } catch (error: any) {
    AMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
    getGroupList()
  }
}

// 删除组
const handleDelete = async (record: GroupRecord) => {
  if (loading.value) return
  try {
    loading.value = true
    await reqDeleteGroup(record.id as string)
    AMessage.success('删除组成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除组失败')
  } finally {
    loading.value = false
    getGroupList()
  }
}

// 打开关联用户模态框
const openUserModal = async (GroupId: string) => {
  currentGroupId.value = GroupId
  userVisible.value = true
  await getGroupUsersData()
}

// 获取本组用户列表
const getGroupUsersData = async () => {
  try {
    userLoading.value = true
    const res = await reqGetUserList({ page: 1, page_size: 999 })
    if (res.code === 200) {
      // 处理用户数据，转换为穿梭框所需格式
      transferData.value = res.data.users.map(user => ({
        value: user.id,
        label: user.username
      }))

      // 过滤出当前组关联的用户ID
      targetKeys.value = res.data.users
        .filter(user => user.groups?.includes(currentGroupId.value))
        .map(user => user.id as string)
    } else {
      AMessage.error(res.message || '获取用户列表失败')
    }
  } finally {
    userLoading.value = false
  }
}

// 关闭用户模态框
const closeUserModal = () => {
  userVisible.value = false
  currentGroupId.value = ''
  transferData.value = []
  targetKeys.value = []
}

// 提交用户关联
const handleUserSubmit = async () => {
  try {
    userLoading.value = true
    await reqUpdateGroupUsers(currentGroupId.value, targetKeys.value)
    AMessage.success('组关联用户成功')
    closeUserModal()
  } catch (error: any) {
    AMessage.error(error.message || '组关联用户失败')
  } finally {
    userLoading.value = false
    getGroupList()
  }
}

// 打开关联主机模态框
const openHostModal = async (groupId: string) => {
  currentGroupId.value = groupId
  hostVisible.value = true
  await getGroupHostsData()
}

// 获取主机列表数据
const getGroupHostsData = async () => {
  try {
    hostLoading.value = true
    const res = await reqGetHostsList({ page: 1, page_size: 999 })
    if (res.code === 200) {
      // 处理主机数据，转换为穿梭框所需格式
      hostTransferData.value = res.data.hosts.map(host => ({
        value: host.id,
        label: host.hostname
      }))

      // 过滤出当前组关联的主机ID
      hostTargetKeys.value = res.data.hosts
        .filter(host => host.groups?.includes(currentGroupId.value))
        .map(host => host.id as string)
    } else {
      AMessage.error(res.message || '获取主机列表失败')
    }
  } finally {
    hostLoading.value = false
  }
}

// 关闭主机模态框
const closeHostModal = () => {
  hostVisible.value = false
  currentGroupId.value = ''
  hostTransferData.value = []
  hostTargetKeys.value = []
}

// 提交主机关联
const handleHostSubmit = async () => {
  try {
    hostLoading.value = true
    await reqUpdateGroupHosts(currentGroupId.value, hostTargetKeys.value)
    AMessage.success('组关联主机成功')
    closeHostModal()
  } catch (error: any) {
    AMessage.error(error.message || '组关联主机失败')
  } finally {
    hostLoading.value = false
    getGroupList()
  }
}

// 重置搜索
const reset = () => {
  searchForm.value?.resetFields()
  getGroupList()
}

// 组件挂载时获取列表
onMounted(() => {
  getGroupList()
})
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="组中心">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="组名称" field="name">
                  <a-input v-model="params.name" placeholder="请输入组名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="组代码" field="code">
                  <a-input v-model="params.code" placeholder="请输入组代码" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 35px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="getGroupList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getGroupList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="groupList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #users="{ record }">
          {{ record.users_count || 0 }}
        </template>
        <template #hosts="{ record }">
          {{ record.hosts_count || 0 }}
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="mini" @click="openEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="mini" @click="openUserModal(record.id)">
              关联用户
            </a-button>
            <a-button type="text" size="mini" @click="openHostModal(record.id)">
              关联主机
            </a-button>
            <a-popconfirm
              :content="`确定要删除${record.name}组吗？`"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button type="text" status="danger" size="mini">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="groupFormRef"
          :model="groupForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="组名称" field="name">
            <a-input v-model="groupForm.name" placeholder="请输入组名称" />
          </a-form-item>
          <a-form-item label="组代码" field="code">
            <a-input v-model="groupForm.code" placeholder="请输入组代码" />
          </a-form-item>
          <a-form-item label="描述" field="description">
            <a-textarea
              v-model="groupForm.description"
              placeholder="请输入组描述"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 关联用户模态框 -->
      <a-modal
        v-model:visible="userVisible"
        title="关联用户"
        @cancel="closeUserModal"
        @ok="handleUserSubmit"
        :loading="userLoading"
        :mask-closable="false"
      >
        <a-transfer
          :data="transferData"
          v-model="targetKeys"
          :show-search="true"
          :title="['此组未添加用户', '此组已添加用户']"
          :source-input-search-props="{
            placeholder: '搜索未添加用户'
          }"
          :target-input-search-props="{
            placeholder: '搜索已添加用户'
          }"
          :height="400"
          :virtual-list-props="{
            height: 400
          }"
        >
        </a-transfer>
      </a-modal>

      <!-- 关联主机模态框 -->
      <a-modal
        v-model:visible="hostVisible"
        title="关联主机"
        @cancel="closeHostModal"
        @ok="handleHostSubmit"
        :loading="hostLoading"
        :mask-closable="false"
      >
        <a-transfer
          :data="hostTransferData"
          v-model="hostTargetKeys"
          :show-search="true"
          :title="['此组未添加主机', '此组已添加主机']"
          :source-input-search-props="{
            placeholder: '搜索未添加主机'
          }"
          :target-input-search-props="{
            placeholder: '搜索已添加主机'
          }"
          :height="400"
          :virtual-list-props="{
            height: 400
          }"
        >
        </a-transfer>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}
</style>
