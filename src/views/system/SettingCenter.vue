<script setup lang="ts">
import type { LDAPConfig, EmailConfig } from '@/types/setting'
import {
  reqTestLDAPConnection,
  reqSaveLDAPConfig,
  reqGetLDAPConfig,
  reqSyncLDAPUsers,
  reqTestEmailConnection,
  reqSaveEmailConfig,
  reqGetEmailConfig
} from '@/api/setting'
// LDAP配置相关
const ldapConfig = reactive<LDAPConfig>({
  enabled: 'disabled',
  host: '',
  port: '388',
  admin_dn: '',
  admin_pwd: '',
  base_dn: '',
  filter: '(&(objectClass=inetOrgPerson)(uid=*))'
})

const getLDAPConfig = async () => {
  loading.value = true
  try {
    const res = await reqGetLDAPConfig()
    if (res.data.host) {
      Object.assign(ldapConfig, res.data)
    }
  } catch (error) {
    AMessage.error('获取LDAP配置失败')
  } finally {
    loading.value = false
  }
}

const getEmailConfig = async () => {
  loading.value = true
  try {
    const res = await reqGetEmailConfig()
    Object.assign(emailConfig, res.data)
  } catch (error) {
    AMessage.error('获取邮件配置失败')
  } finally {
    loading.value = false
  }
}

// Email配置相关
const emailConfig = reactive<EmailConfig>({
  enabled: 'disabled',
  smtp_host: '',
  smtp_port: 465,
  smtp_user: '',
  smtp_pass: '',
  smtp_send_name: ''
})

// 修改loading状态管理为单个状态
const loading = ref(false)

const activeTab = ref('1')

// LDAP相关方法
const testLDAPConnection = async () => {
  loading.value = true
  try {
    await reqTestLDAPConnection(ldapConfig)
    AMessage.success('LDAP连接测试成功')
  } catch (error) {
    AMessage.error('LDAP连接测试失败')
  } finally {
    loading.value = false
  }
}

const syncUsers = async () => {
  loading.value = true
  try {
    const res = await reqSyncLDAPUsers()
    AMessage.success(res.message || '用户同步成功')
  } catch (error: any) {
    AMessage.error('用户同步失败')
  } finally {
    loading.value = false
  }
}

// Email相关方法
const testEmailConnection = async () => {
  loading.value = true
  try {
    await reqTestEmailConnection(emailConfig)
    AMessage.success('邮件服务器连接测试成功')
  } catch (error) {
    AMessage.error('邮件服务器连接测试失败')
  } finally {
    loading.value = false
  }
}

const saveConfig = async (type: 'ldap' | 'email') => {
  loading.value = true
  try {
    if (type === 'ldap') {
      await reqSaveLDAPConfig(ldapConfig)
    } else if (type === 'email') {
      await reqSaveEmailConfig(emailConfig)
    }
    AMessage.success('配置保存成功')
  } catch (error) {
    AMessage.error('配置保存失败')
  } finally {
    if (type === 'ldap') {
      getLDAPConfig()
    } else if (type === 'email') {
      getEmailConfig()
    }
    loading.value = false
  }
}

onMounted(() => {
  getLDAPConfig()
  getEmailConfig()
})
</script>

<template>
  <div class="container">
    <div v-if="loading" class="spin-overlay">
      <a-spin dot />
    </div>
    <div class="setting-card">
      <a-tabs
        v-model:activeKey="activeTab"
        size="large"
        type="line"
        direction="vertical"
      >
        <!-- LDAP配置标签页 -->
        <a-tab-pane key="1" title="LDAP认证配置">
          <a-card class="card-container" title="LDAP认证配置">
            <a-form
              :model="ldapConfig"
              :style="{ width: '600px' }"
              size="small"
            >
              <a-form-item label="启用LDAP认证">
                <a-switch
                  v-model="ldapConfig.enabled"
                  checked-value="enabled"
                  unchecked-value="disabled"
                  type="round"
                >
                  <template #checked> ON </template>
                  <template #unchecked> OFF </template>
                  <template #checked-icon>
                    <icon-material-symbols-check-rounded class="icon" />
                  </template>
                  <template #unchecked-icon>
                    <icon-material-symbols-close-rounded class="icon" />
                  </template>
                </a-switch>
              </a-form-item>

              <a-form-item field="host" label="LDAP 服务地址" required>
                <a-input
                  v-model="ldapConfig.host"
                  placeholder="请输入LDAP服务地址"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="port" label="LDAP 服务端口" required>
                <a-input v-model="ldapConfig.port" />
              </a-form-item>

              <a-form-item field="admin_dn" label="管理员 DN" required>
                <a-input
                  v-model="ldapConfig.admin_dn"
                  placeholder="例如：cn=admin,dc=example,dc=com"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="admin_pwd" label="管理员密码">
                <a-input
                  v-model="ldapConfig.admin_pwd"
                  placeholder="请输入管理员密码"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="base_dn" label="基本DN" required>
                <a-input
                  v-model="ldapConfig.base_dn"
                  placeholder="例如：dc=example,dc=com"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="filter" label="LDAP搜索规则" required>
                <a-input
                  v-model="ldapConfig.filter"
                  placeholder="请输入LDAP搜索规则"
                  allow-clear
                />
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button type="primary" @click="testLDAPConnection">
                    测试LDAP连接
                  </a-button>
                  <a-button type="primary" @click="saveConfig('ldap')">
                    保存配置
                  </a-button>
                  <a-button type="primary" @click="syncUsers">
                    立即同步用户
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </a-tab-pane>

        <!-- Email配置标签页 -->
        <a-tab-pane key="2" title="Email推送服务">
          <a-card class="card-container" title="Email推送服务">
            <a-form
              :model="emailConfig"
              :style="{ width: '600px' }"
              size="small"
            >
              <a-form-item label="启用邮件服务">
                <a-switch
                  v-model="emailConfig.enabled"
                  checked-value="enabled"
                  unchecked-value="disabled"
                  type="round"
                >
                  <template #checked> ON </template>
                  <template #unchecked> OFF </template>
                  <template #checked-icon>
                    <icon-material-symbols-check-rounded class="icon" />
                  </template>
                  <template #unchecked-icon>
                    <icon-material-symbols-close-rounded class="icon" />
                  </template>
                </a-switch>
              </a-form-item>

              <a-form-item field="smtp_host" label="SMTP 服务器" required>
                <a-input
                  v-model="emailConfig.smtp_host"
                  placeholder="例如：smtp.example.com"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="smtp_port" label="SMTP 端口" required>
                <a-input-number
                  v-model="emailConfig.smtp_port"
                  placeholder="端口"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="smtp_user" label="SMTP 用户名" required>
                <a-input
                  v-model="emailConfig.smtp_user"
                  placeholder="请输入SMTP用户名"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="smtp_pass" label="SMTP 密码" required>
                <a-input-password
                  v-model="emailConfig.smtp_pass"
                  placeholder="请输入SMTP密码"
                  allow-clear
                />
              </a-form-item>

              <a-form-item field="smtp_send_name" label="发件人名称">
                <a-input
                  v-model="emailConfig.smtp_send_name"
                  placeholder="请输入发件人名称"
                  allow-clear
                />
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button type="primary" @click="testEmailConnection">
                    测试连接
                  </a-button>
                  <a-button type="primary" @click="saveConfig('email')">
                    保存配置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-card {
  background-color: var(--color-bg-2);
  padding: 50px;
  border-radius: 4px;
  border: 1px solid var(--color-neutral-3);
}

.card-container {
  border-radius: 4px;
  border: none;

  > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }

  > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.icon {
  padding-top: 8px;
}
</style>
