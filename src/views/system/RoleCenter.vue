<script setup lang="ts">
// 导入角色相关的类型和API
import type { RoleRecord, RoleListParams } from '@/types/role'
import {
  reqGetRoleList,
  reqAddRole,
  reqDeleteRole,
  reqUpdateRole,
  reqUpdateRoleUsers
} from '@/api/role'
import { reqGetUserList } from '@/api/user'
import { reqGetMenuList } from '@/api/menu'
import type { MenuRecord } from '@/types/menu'
import type { FieldRule } from '@arco-design/web-vue'
import { reqGetApiList, reqGetApiGroups } from '@/api/api'

// 查询参数
const params = reactive<RoleListParams>({
  page: 1,
  page_size: 10
})

// 表格列配置
const columns = [
  { title: '角色名称', dataIndex: 'name' },
  { title: '用户数量', dataIndex: 'users_count', slotName: 'users' },
  { title: '描述', dataIndex: 'description' },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 200
  }
]

// 加载状态
const loading = ref(false)
// 数据总条数
const total = ref(0)
// 搜索表单引用
const searchForm = ref()

// 分页配置
const pagination = computed(() => ({
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30, 50],
  total: total.value,
  current: params.page,
  pageSize: params.page_size
}))

// 角色列表数据
const roleList = ref<RoleRecord[]>([])

// 获取角色列表
const getRoleList = async () => {
  try {
    loading.value = true
    const res = await reqGetRoleList(params)
    roleList.value = res.data.roles
    total.value = res.data.total
  } catch (error: any) {
    AMessage.error(error.message || '获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 页码改变回调
const onPageChange = (page: number) => {
  params.page = page
  getRoleList()
}

// 每页条数改变回调
const onPageSizeChange = (pageSize: number) => {
  params.page_size = pageSize
  getRoleList()
}

// 重置搜索表单
const reset = () => {
  searchForm.value?.resetFields()
  getRoleList()
}

// 组件挂载时获取列表
onMounted(() => {
  getRoleList()
})

// 新增/编辑模态框相关状态
const visible = ref(false)
const modalType = ref<'add' | 'edit'>('add')
const roleForm = reactive<RoleRecord>({
  name: ''
})
const roleFormRef = ref()

// 表单校验规则
const rules: Record<string, FieldRule | FieldRule[]> = {
  name: [{ required: true, message: '请输入角色名称' }]
}

// 模态框标题
const modalTitle = computed(() =>
  modalType.value === 'add' ? '添加角色' : '编辑角色'
)

// 打开新增模态框
const openAddModal = () => {
  modalType.value = 'add'
  visible.value = true
}

// 打开编辑模态框
const openEditModal = (record: RoleRecord) => {
  modalType.value = 'edit'
  Object.assign(roleForm, record)
  visible.value = true
}

// 关闭模态框
const closeModal = () => {
  visible.value = false
  roleFormRef.value?.resetFields()
}

// 表单提交前校验
const checkFormRule = async () => {
  const res = await roleFormRef.value.validate()
  return !res
}

// 提交表单(新增/编辑)
const handleSubmit = async () => {
  try {
    loading.value = true
    modalType.value === 'add'
      ? await reqAddRole(roleForm)
      : await reqUpdateRole(roleForm)

    AMessage.success(
      modalType.value === 'add' ? '添加角色成功' : '编辑角色成功'
    )
  } catch (error: any) {
    AMessage.error(
      error.message ||
        (modalType.value === 'add' ? '添加角色失败' : '编辑角色失败')
    )
  } finally {
    loading.value = false
    closeModal()
    getRoleList()
  }
}

// 删除角色
const handleDelete = async (record: RoleRecord) => {
  try {
    loading.value = true
    await reqDeleteRole(record.id as string)
    AMessage.success('删除角色成功')
  } catch (error: any) {
    AMessage.error(error.message || '删除角色失败')
  } finally {
    loading.value = false
    getRoleList()
  }
}

// 用户关联模态框相关状态
const userVisible = ref(false)
const currentRoleId = ref('')
const userLoading = ref(false)
const transferData = ref<any[]>([])
const targetKeys = ref<string[]>([])

// 打开关联用户模态框
const openUserModal = async (roleId: string) => {
  currentRoleId.value = roleId
  userVisible.value = true
  await getRoleUsersData()
}

// 获取用户列表数据
const getRoleUsersData = async () => {
  try {
    userLoading.value = true
    const res = await reqGetUserList({ page: 1, page_size: 1999 })
    console.log(res.data)
    if (res.code === 200) {
      // 转换为穿梭框需要的数据格式，并禁用 admin 用户
      transferData.value = res.data.users.map(user => ({
        value: user.id,
        label: user.username,
        disabled: user.loginname === 'admin' // 禁用 admin 用户
      }))

      // 过滤出当前角色关联的用户ID
      targetKeys.value = res.data.users
        .filter(user => user.roles?.includes(currentRoleId.value))
        .map(user => user.id as string)
    } else {
      AMessage.error(res.message || '获取用户列表失败')
    }
  } finally {
    userLoading.value = false
  }
}

// 关闭用户关联模态框
const closeUserModal = () => {
  userVisible.value = false
  currentRoleId.value = ''
  transferData.value = []
  targetKeys.value = []
}

// 提交用户关联
const handleUserSubmit = async () => {
  try {
    userLoading.value = true
    await reqUpdateRoleUsers(currentRoleId.value, targetKeys.value)
    AMessage.success('角色关联用户成功')
    closeUserModal()
  } catch (error: any) {
    AMessage.error(error.message || '角色关联用户失败')
  } finally {
    userLoading.value = false
    getRoleList()
  }
}

// 权限分配模态框相关状态
const permissionVisible = ref(false)
const currentPermissionRoleId = ref('')
const menuLoading = ref(false)
const menuList = ref<MenuRecord[]>([])
const checkedMenuKeys = ref<string[]>([])

// API相关状态
const apiLoading = ref(false)
const apiList = ref<ApiTreeNode[]>([])
const checkedApiKeys = ref<string[]>([])

// 获取菜单列表
const getMenuList = async () => {
  try {
    menuLoading.value = true
    const res = await reqGetMenuList({ page: 1, page_size: 999 })
    menuList.value = res.data.menus
  } catch (error: any) {
    console.error('获取菜单列表失败:', error)
  } finally {
    menuLoading.value = false
  }
}

// API树节点的类型定义
interface ApiTreeNode {
  id: string // 节点ID：分组节点以'group_'开头，API节点使用原始ID
  name: string // 节点名称：分组名或API名称
  path?: string // API路径：仅API节点有此属性，包含请求方法和路径
  children?: ApiTreeNode[] // 子节点：分组节点包含API节点列表
}

// 获取API列表并转换为树形结构
const getApiList = async () => {
  try {
    apiLoading.value = true
    // 并行获取API列表和分组列表
    const [apiRes, groupRes] = await Promise.all([
      reqGetApiList({ page: 1, page_size: 999 }),
      reqGetApiGroups()
    ])

    // 将API按组转换为树形结构
    const apiTree: ApiTreeNode[] = groupRes.data.groups.map(group => ({
      id: `group_${group}`, // 分组节点ID添加前缀，用于区分API节点
      name: group, // 分组名称
      children: apiRes.data.apis
        .filter(api => api.group === group) // 过滤出属于当前分组的API
        .map(api => ({
          id: api.id as string, // API节点使用原始ID
          name: api.name, // API名称
          path: `[${api.method}] ${api.path}` // 组合显示请求方法和路径
        }))
    }))

    apiList.value = apiTree
  } catch (error: any) {
    console.error('获取API列表失败:', error)
  } finally {
    apiLoading.value = false
  }
}

// 当前编辑的角色数据
const currentRole = ref<RoleRecord | null>(null)

// 打开权限分配模态框
const openPermissionModal = async (record: RoleRecord) => {
  currentRole.value = { ...record } // 保存完整的角色数据，用于后续更新
  currentPermissionRoleId.value = record.id as string
  checkedMenuKeys.value = record.menu_ids || [] // 初始化已选菜单
  checkedApiKeys.value = record.api_ids || [] // 初始化已选API
  permissionVisible.value = true
  // 并行加载菜单和API数据
  await Promise.all([getMenuList(), getApiList()])
}

// 提交权限设置
const handlePermissionSubmit = async () => {
  if (!currentRole.value) return

  try {
    loading.value = true
    await reqUpdateRole({
      ...currentRole.value,
      menu_ids: checkedMenuKeys.value,
      // 过滤掉分组节点ID，只保留实际的API ID
      api_ids: checkedApiKeys.value.filter(id => !id.startsWith('group_'))
    })
    AMessage.success('更新角色权限成功')
    closePermissionModal()
    getRoleList()
  } catch (error: any) {
    AMessage.error(error.message || '更新角色权限失败')
  } finally {
    loading.value = false
  }
}

// 关闭权限分配模态框并清空状态
const closePermissionModal = () => {
  permissionVisible.value = false
  currentPermissionRoleId.value = ''
  checkedMenuKeys.value = []
  checkedApiKeys.value = []
  currentRole.value = null
  apiList.value = []
}

// 处理树选择变化
const handleTreeCheck = (checkedKeys: string[]) => {
  checkedMenuKeys.value = checkedKeys
}
</script>

<template>
  <div class="container">
    <a-card class="general-card" title="角色管理">
      <!-- 搜索区域 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            ref="searchForm"
            :model="params"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="角色名称" field="name">
                  <a-input v-model="params.name" placeholder="请输入角色名称" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 35px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="getRoleList">
              <template #icon>
                <icon-material-symbols-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-bx-reset />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <!-- 操作按钮区域 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="openAddModal">
            <template #icon>
              <icon-material-symbols-add />
            </template>
            新增
          </a-button>
        </a-col>
        <a-col
          :span="12"
          style="display: flex; align-items: center; justify-content: end"
        >
          <a-tooltip content="刷新">
            <div class="action-icon" @click="getRoleList">
              <icon-tabler-refresh />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data="roleList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #users="{ record }">
          {{ record.users_count || 0 }}
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button
              type="text"
              size="mini"
              @click="openEditModal(record)"
              :disabled="record.name === '超级管理员'"
            >
              编辑
            </a-button>
            <a-button type="text" size="mini" @click="openUserModal(record.id)">
              关联用户
            </a-button>
            <a-button
              type="text"
              size="mini"
              @click="openPermissionModal(record)"
              :disabled="record.name === '超级管理员'"
            >
              分配权限
            </a-button>
            <a-popconfirm
              :content="`确定要删除${record.name}角色吗`"
              type="warning"
              @ok="handleDelete(record)"
            >
              <a-button
                type="text"
                status="danger"
                size="mini"
                :disabled="record.name === '超级管理员'"
              >
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>

      <!-- 添加/编辑模态框 -->
      <a-modal
        v-model:visible="visible"
        :title="modalTitle"
        @cancel="closeModal"
        @ok="handleSubmit"
        :on-before-ok="checkFormRule"
      >
        <a-form
          ref="roleFormRef"
          :model="roleForm"
          :rules="rules"
          label-align="right"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item label="角色名称" field="name">
            <a-input v-model="roleForm.name" placeholder="请输入角色名称" />
          </a-form-item>
          <a-form-item label="描述" field="description">
            <a-textarea
              v-model="roleForm.description"
              placeholder="请输入角色描述"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 关联用户模态框 -->
      <a-modal
        v-model:visible="userVisible"
        title="关联用户"
        @cancel="closeUserModal"
        @ok="handleUserSubmit"
        :loading="userLoading"
        :mask-closable="false"
      >
        <a-transfer
          :data="transferData"
          v-model="targetKeys"
          :show-search="true"
          :title="['此角色未添加用户', '此角色已添加用户']"
          :source-input-search-props="{
            placeholder: '搜索未添加用户'
          }"
          :target-input-search-props="{
            placeholder: '搜索已添加用户'
          }"
        >
        </a-transfer>
      </a-modal>

      <!-- 分配权限模态框 -->
      <a-modal
        v-model:visible="permissionVisible"
        :hide-title="true"
        @cancel="closePermissionModal"
        @ok="handlePermissionSubmit"
        :mask-closable="false"
      >
        <a-tabs default-active-key="1">
          <a-tab-pane key="1" title="菜单权限">
            <a-spin :loading="menuLoading">
              <a-tree
                :data="menuList"
                v-model:checked-keys="checkedMenuKeys"
                :checkable="true"
                :check-strictly="false"
                :field-names="{
                  key: 'id',
                  title: 'name',
                  children: 'children'
                }"
              />
            </a-spin>
          </a-tab-pane>
          <a-tab-pane key="2" title="API权限">
            <a-spin :loading="apiLoading">
              <a-tree
                :data="apiList"
                v-model:checked-keys="checkedApiKeys"
                :checkable="true"
                :check-strictly="false"
                :field-names="{
                  key: 'id',
                  title: 'name',
                  children: 'children'
                }"
              >
                <template #title="nodeData">
                  <div class="api-node">
                    <span class="api-name">{{ nodeData.name }}</span>
                    <span v-if="nodeData.path" class="api-path">{{
                      nodeData.path
                    }}</span>
                  </div>
                </template>
              </a-tree>
            </a-spin>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.api-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 16px;

  .api-name {
    flex-shrink: 0;
  }

  .api-path {
    color: var(--color-neutral-6);
    font-weight: 500;
    font-size: 14px;
  }
}
</style>
