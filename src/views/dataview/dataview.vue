<template>
  <div class="container">
    <a-spin class="container" :loading="loading" tip="请稍候，数据正在获取中">
      <a-card class="general-card" title="经户实时数据">
        <a-typography-text type="secondary">
          实时查看个体/企业最新注册信息，以及截止当前数据总量，数据每隔 5
          秒刷新一次。
        </a-typography-text>
        <a-divider />
        <a-row justify="space-around">
          <a-col :span="4">
            <div class="stat-card">
              <div class="icon-wrapper">
                <a-avatar :size="50" :style="{ backgroundColor: '#F2F3F5' }">
                  <icon-wpf-statistics style="color: #01a96c" />
                </a-avatar>
              </div>
              <div class="content">
                <div class="title">市场主体总量</div>
                <a-statistic
                  :start="false"
                  :value="renderData.count"
                  show-group-separator
                  animation
                  :value-style="{
                    paddingTop: '5px',
                    fontSize: '20px',
                    fontWeight: 'bold'
                  }"
                />
              </div>
            </div>
          </a-col>
          <a-col :span="4">
            <div class="stat-card">
              <div class="icon-wrapper">
                <a-avatar :size="50" :style="{ backgroundColor: '#F2F3F5' }">
                  <icon-carbon-enterprise style="color: #01a96c" />
                </a-avatar>
              </div>
              <div class="content">
                <div class="title">存活企业数量</div>
                <a-statistic
                  :start="false"
                  :value="renderData.qy_count"
                  show-group-separator
                  animation
                  :value-style="{
                    paddingTop: '5px',
                    fontSize: '20px',
                    fontWeight: 'bold'
                  }"
                />
              </div>
            </div>
          </a-col>
          <a-col :span="4">
            <div class="stat-card">
              <div class="icon-wrapper">
                <a-avatar :size="50" :style="{ backgroundColor: '#F2F3F5' }">
                  <icon-mingcute-shop-line style="color: #01a96c" />
                </a-avatar>
              </div>
              <div class="content">
                <div class="title">存活个体数量</div>
                <a-statistic
                  :start="false"
                  :value="renderData.gt_count"
                  show-group-separator
                  animation
                  :value-style="{
                    paddingTop: '5px',
                    fontSize: '20px',
                    fontWeight: 'bold'
                  }"
                />
              </div>
            </div>
          </a-col>
          <a-col :span="4">
            <div class="stat-card">
              <div class="icon-wrapper">
                <a-avatar :size="50" :style="{ backgroundColor: '#F2F3F5' }">
                  <icon-lucide-land-plot style="color: #01a96c" />
                </a-avatar>
              </div>
              <div class="content">
                <div class="title">存活农专数量</div>
                <a-statistic
                  :start="false"
                  :value="renderData.nz_count"
                  show-group-separator
                  animation
                  :value-style="{
                    paddingTop: '5px',
                    fontSize: '20px',
                    fontWeight: 'bold'
                  }"
                />
              </div>
            </div>
          </a-col>
        </a-row>
        <a-card title="最新 10 条企业注册信息" :bordered="false">
          <a-table
            :columns="columns"
            :data="renderData.qy_top_table"
            :pagination="false"
            :bordered="false"
          />
        </a-card>
        <a-card title="最新 10 条个体注册信息" :bordered="false">
          <a-table
            :columns="columns"
            :data="renderData.gt_top_table"
            :pagination="false"
            :bordered="false"
          />
        </a-card>
      </a-card>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import { reqGetDataViewList } from '@/api/dataview'

const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 600
  },
  {
    title: '信用代码',
    dataIndex: 'code'
  },
  {
    title: '注册时间',
    dataIndex: 'time'
  }
]

const loading = ref(true)

let renderData = ref<any>({})

// 获取数据
const fetchData = async () => {
  if (renderData.value.count == 0) {
    loading.value = true
  }

  try {
    const { data } = await reqGetDataViewList()
    renderData.value = data
    setTimeout(fetchData, 5000)
  } catch (err: any) {
    AMessage.error(err.message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 500;
}

.stat-card {
  background-color: var(--color-bg-1);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: start;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.icon-wrapper {
  font-size: 24px;
  margin-right: 16px;
}
.content {
  .title {
    color: var(--color-text-2);
    font-size: 14px;
    margin-bottom: 4px;
  }
}
</style>
