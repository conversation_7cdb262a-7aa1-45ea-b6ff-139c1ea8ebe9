<script setup lang="ts">
import { useUserStore, useSystemStore } from '@/stores'
import {
  reqGetNavList,
  reqAddNav,
  reqDeleteNav,
  reqGetDashboard
} from '@/api/dashboard'
import type { NavRecord, DashboardRecord } from '@/types/dashboard'

import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import type { EChartsOption } from 'echarts'

use([
  CanvasRenderer,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
])

const userStore = useUserStore()
const systemStore = useSystemStore()

const hostGroupData = ref<any[]>([])

// 仪表盘数据
const dashboardData = ref<DashboardRecord>({
  offline_host: 0,
  online_host: 0,
  ticket_todo: 0,
  ticket_toward: 0,
  pie_data: []
})

// 导航列表
const navList = ref<NavRecord[]>([])

// 是否显示添加导航模态框
const showAddModal = ref(false)
// 是否处于编辑模式
const isEditMode = ref(false)
// 新导航表单
const newNav = ref<NavRecord>({
  name: '',
  url: '',
  is_admin: false
})

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    const { data } = await reqGetDashboard()
    dashboardData.value = data
    hostGroupData.value = data.pie_data.map((item, index) => ({
      ...item,
      itemStyle: { color: colors[index % colors.length] }
    }))
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  }
}

// 获取导航列表
const fetchNavList = async () => {
  try {
    const { data } = await reqGetNavList()
    navList.value = data
  } catch (error) {
    console.error('获取导航列表失败:', error)
  }
}

// 添加导航
const handleAddNav = async () => {
  try {
    await reqAddNav(newNav.value)
    AMessage.success('添加成功')
    showAddModal.value = false
    newNav.value = { name: '', url: '', is_admin: false }
    await fetchNavList()
  } catch (error) {
    AMessage.error('添加失败')
  }
}

// 删除导航
const handleDeleteNav = async (id: string) => {
  try {
    await reqDeleteNav(id)
    AMessage.success('删除成功')
    await fetchNavList()
  } catch (error) {
    AMessage.error('删除失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchDashboardData()
  fetchNavList()
})

const colors = [
  '#6B74E6',
  '#FF9466',
  '#95D475',
  '#8B5CF6',
  '#FFD666',
  '#FF85C0',
  '#FF6B6B',
  '#739BE8',
  '#73D8FF',
  '#9FDB1D',
  '#4CD263',
  '#FFB347'
]

// 获取当前主题模式
const isDarkMode = computed(() => systemStore.currentMode?.name === 'dark')

// 饼图配置
const pieChartOption = computed<EChartsOption>(() => ({
  title: {
    text: '运维单位主机数量分布',
    subtext: '采集数量为系统录入节点为基础数据',
    left: 'center',
    textStyle: {
      color: isDarkMode.value ? '#fff' : '#000'
    },
    subtextStyle: {
      color: isDarkMode.value
        ? 'rgba(255, 255, 255, 0.7)'
        : 'rgba(0, 0, 0, 0.7)'
    }
  },
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      color: isDarkMode.value ? '#fff' : '#000'
    }
  },
  series: [
    {
      name: '节点数量',
      type: 'pie',
      radius: '50%',
      data: hostGroupData.value,
      label: {
        color: isDarkMode.value ? '#fff' : '#444'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: isDarkMode.value
            ? 'rgba(255, 255, 255, 0.3)'
            : 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))
</script>
<template>
  <div class="container">
    <a-row :gutter="20">
      <!-- 左侧 -->
      <a-col :span="19">
        <a-space direction="vertical" size="large" fill>
          <a-row>
            <a-col :span="24">
              <div class="title-box">
                <span> 欢迎回来，{{ userStore.userInfo?.username }}！ </span>
              </div>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <div class="stat-box">
                <a-row :gutter="24">
                  <!-- 统计卡片1-->
                  <a-col
                    v-if="userStore.userInfo.role_names?.includes('超级管理员')"
                    :span="12"
                  >
                    <div class="stat-card">
                      <div class="icon-wrapper">
                        <a-avatar
                          :size="50"
                          :style="{ backgroundColor: '#F2F3F5' }"
                        >
                          <icon-fa6-solid-server style="color: #01a96c" />
                        </a-avatar>
                      </div>
                      <div class="content">
                        <div class="title">存活主机数量</div>
                        <a-statistic
                          :value="dashboardData.online_host"
                          show-group-separator
                          animation
                          :value-style="{
                            paddingTop: '5px',
                            fontSize: '20px',
                            fontWeight: 'bold'
                          }"
                        />
                      </div>
                    </div>
                  </a-col>
                  <!-- 统计卡片2 -->
                  <a-col
                    v-if="userStore.userInfo.role_names?.includes('超级管理员')"
                    :span="12"
                  >
                    <div class="stat-card">
                      <div class="icon-wrapper">
                        <a-avatar
                          :size="50"
                          :style="{ backgroundColor: '#F2F3F5' }"
                        >
                          <icon-mdi-offline style="color: #ff756e" />
                        </a-avatar>
                      </div>
                      <div class="content">
                        <div class="title">疑似主机宕机数量</div>
                        <a-statistic
                          :value="dashboardData.offline_host"
                          show-group-separator
                          animation
                          :value-style="{
                            paddingTop: '5px',
                            fontSize: '20px',
                            fontWeight: 'bold',
                            color: 'red'
                          }"
                        />
                      </div>
                    </div>
                  </a-col>

                  <!-- 统计卡片3-->
                  <a-col :span="12">
                    <div class="stat-card">
                      <div class="icon-wrapper">
                        <a-avatar
                          :size="50"
                          :style="{ backgroundColor: '#F2F3F5' }"
                        >
                          <icon-material-symbols-pending-actions
                            style="color: #237803"
                          />
                        </a-avatar>
                      </div>
                      <div class="content">
                        <div class="title">待办工单数量</div>
                        <a-statistic
                          :value="dashboardData.ticket_todo"
                          show-group-separator
                          animation
                          :value-style="{
                            paddingTop: '5px',
                            fontSize: '20px',
                            fontWeight: 'bold'
                          }"
                        />
                      </div>
                    </div>
                  </a-col>

                  <!-- 统计卡片4-->
                  <a-col :span="12">
                    <div class="stat-card">
                      <div class="icon-wrapper">
                        <a-avatar
                          :size="50"
                          :style="{ backgroundColor: '#F2F3F5' }"
                        >
                          <icon-mdi-progress-check style="color: #ebc85a" />
                        </a-avatar>
                      </div>
                      <div class="content">
                        <div class="title">待处理工单数量</div>
                        <a-statistic
                          :value="dashboardData.ticket_toward"
                          show-group-separator
                          animation
                          :value-style="{
                            paddingTop: '5px',
                            fontSize: '20px',
                            fontWeight: 'bold'
                          }"
                        />
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </div>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <div
                v-if="userStore.userInfo.role_names?.includes('超级管理员')"
                class="chart-box"
              >
                <v-chart :option="pieChartOption" autoresize />
              </div>
              <div v-else class="chart-box">
                <a-empty description="暂无权限查看" />
              </div>
            </a-col>
          </a-row>
        </a-space>
      </a-col>
      <a-col :span="5">
        <div class="quick-navigation">
          <div class="header">
            <span class="title">快捷导航</span>
            <a-space
              v-if="userStore.userInfo.role_names?.includes('超级管理员')"
            >
              <a-button type="primary" size="mini" @click="showAddModal = true">
                <template #icon>
                  <icon-material-symbols-add-rounded />
                </template>
              </a-button>
              <a-button
                type="primary"
                size="mini"
                @click="isEditMode = !isEditMode"
                :status="isEditMode ? 'warning' : 'normal'"
              >
                <template #icon>
                  <Icon-tabler-edit />
                </template>
              </a-button>
            </a-space>
          </div>
          <div class="links">
            <div v-for="nav in navList" :key="nav.id">
              <a-row
                v-if="
                  !nav.is_admin ||
                  (nav.is_admin &&
                    userStore.userInfo.role_names?.includes('超级管理员'))
                "
                :gutter="2"
              >
                <a-col :span="22" style="padding-bottom: 3px">
                  <a-tooltip :content="nav.name">
                    <a-link
                      :href="nav.url"
                      icon
                      target="_blank"
                      class="nav-link"
                      >{{ nav.name }}</a-link
                    >
                  </a-tooltip>
                </a-col>
                <a-col :span="2">
                  <a-button
                    v-if="isEditMode"
                    type="text"
                    status="danger"
                    size="mini"
                    @click="handleDeleteNav(nav.id!)"
                  >
                    <template #icon>
                      <icon-material-symbols-delete />
                    </template>
                  </a-button>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 添加导航模态框 -->
    <a-modal
      v-model:visible="showAddModal"
      title="添加快捷导航"
      @ok="handleAddNav"
      @cancel="showAddModal = false"
    >
      <a-form :model="newNav">
        <a-form-item label="名称" required>
          <a-input v-model="newNav.name" placeholder="请输入导航名称" />
        </a-form-item>
        <a-form-item label="链接" required>
          <a-input v-model="newNav.url" placeholder="请输入链接地址" />
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model="newNav.is_admin">仅管理员可见</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<style scoped lang="scss">
.title-box {
  background-color: var(--color-bg-2);
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 18px;
  font-weight: 500;
}

.stat-box {
  background-color: var(--color-bg-2);
  height: 200px;

  .stat-card {
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: start;
  }
  .icon-wrapper {
    font-size: 24px;
    margin-right: 16px;
  }
  .content {
    .title {
      color: var(--color-text-2);
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }
  }
}

.chart-box {
  background-color: var(--color-bg-2);
  height: calc(100vh - 58px - 30px - 50px - 200px - 80px);
  padding: 16px;
}

.quick-navigation {
  min-width: 150px;
  height: calc(100vh - 58px - 30px);
  background-color: var(--color-bg-2);

  .header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--color-neutral-2);
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }

  .links {
    padding: 10px;

    .nav-link {
      display: block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
