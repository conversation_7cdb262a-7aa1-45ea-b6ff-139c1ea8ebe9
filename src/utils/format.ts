import dayjs from 'dayjs'

// 格式化时间戳
export const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-'
  return dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化字节大小
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export function formatBytes(bytes: number, k: number = 1024): string {
  if (bytes === 0) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

/**
 * 格式化 GB 大小
 * @param gigaBytes GB
 * @returns 格式化后的字符串
 */
export function formatGigaBytes(gigaBytes: number): string {
  // 将 GB 转换为字节
  const bytes = gigaBytes * Math.pow(1000, 3)
  return formatBytes(bytes, 1000)
}

/**
 * 格式化网速
 * @param bytesPerSecond 每秒字节数
 * @returns 格式化后的字符串
 */
export function formatSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond === 0) return '0 K/s'
  // 直接将字节转换为 KB
  const kiloBytes = bytesPerSecond / 1024
  const k = 1024
  const sizes = ['K', 'M', 'G', 'T']

  if (kiloBytes < 1) {
    return `${kiloBytes.toFixed(2)} K/s`
  }

  const i = Math.floor(Math.log(kiloBytes) / Math.log(k))
  return `${(kiloBytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}/s`
}

/**
 * 获取指定时间戳到现在的天数
 * @param timestamp 时间戳（秒）
 * @returns 天数
 */
export function getDaysDiff(timestamp: number): number {
  if (!timestamp) return 0
  const now = dayjs()
  const date = dayjs(timestamp * 1000)
  return now.diff(date, 'day')
}

/**
 * 将秒转换为天数
 * @param seconds 秒数
 * @returns 天数
 */
export function secondsToDays(seconds: number): number {
  if (!seconds) return 0
  return Math.floor(seconds / (24 * 60 * 60))
}
