import axios, { AxiosError } from 'axios'
import { useUserStore } from '@/stores'

// 添加一个标志位来防止重复处理 401
let isRefreshing = false

const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 10 * 1000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 如果是 FormData 类型的请求（文件上传），不设置 Content-Type
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type']
    } else {
      // 其他请求设置 JSON Content-Type
      config.headers['Content-Type'] = 'application/json'
    }

    // 获取 token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果是二进制流，直接返回响应
    if (response.config.responseType === 'blob') {
      return response
    }

    // 处理普通 JSON 响应
    if (response.data.code === 200) {
      return response.data
    }
    // 业务错误，抛出错误让组件处理
    return Promise.reject(new Error(response.data.message))
  },
  async (error: AxiosError) => {
    // HTTP 错误直接在这里处理，不再向后传递
    const status = error.response?.status

    switch (status) {
      case 401:
        // 防止重复处理 401 错误
        if (!isRefreshing) {
          isRefreshing = true
          AMessage.warning({
            content: 'Token已过期，请重新登录',
            duration: 3000
          })
          const userStore = useUserStore()
          await userStore.Logout() // 确保等待登出操作完成

          setTimeout(() => {
            isRefreshing = false
          }, 1500)
        }
        break
      case 403:
        AMessage.error('暂无权限，请联系管理员')
        break
      case 404:
        AMessage.error('请求的资源不存在')
        break
      case 500:
        AMessage.error('服务器内部错误，请稍后重试')
        break
      default:
        AMessage.error('网络异常，请检查网络连接')
        break
    }
    return Promise.reject(error)
  }
)

export default service
