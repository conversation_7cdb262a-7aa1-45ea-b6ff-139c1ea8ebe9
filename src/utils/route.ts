import type { RouteRecordRaw } from 'vue-router'

/**
 * 格式化路由对象，主要用于处理路由的显示逻辑
 * @description 该函数主要完成以下功能：
 * 1. 过滤掉被标记为隐藏的路由（meta.hidden 为 true）
 * 2. 优化路由层级结构：
 *    - 如果某个路由的所有子路由都被隐藏，则该路由也会被过滤掉
 *    - 如果某个路由只有一个可见的子路由，则直接使用该子路由
 *    - 如果有多个可见的子路由，则保持原有的层级结构
 * @param routes 原始路由配置数组
 * @returns 处理后的路由配置数组
 */
export const formatRoutes = (routes: RouteRecordRaw[]): RouteRecordRaw[] => {
  const result: RouteRecordRaw[] = []

  routes.forEach(route => {
    // 如果路由被标记为隐藏，直接跳过
    if (route.meta?.hidden) {
      return
    }

    // 处理含有 children 的路由
    if (route.children && route.children.length > 0) {
      // 过滤掉 hidden 为 true 的子路由
      const visibleChildren = route.children.filter(
        child => !child.meta?.hidden
      )

      if (visibleChildren.length === 0) {
        // 如果过滤后没有可见的子路由，跳过当前路由
        return
      } else if (visibleChildren.length === 1) {
        // 如果只有一个可见的子路由，直接添加这个子路由
        result.push(visibleChildren[0])
      } else {
        // 如果有多个可见的子路由，保持完整的层级结构
        const newRoute = { ...route }
        newRoute.children = formatRoutes(visibleChildren)
        result.push(newRoute)
      }
    } else {
      // 没有子路由的情况，直接添加当前路由
      result.push(route)
    }
  })

  return result
}

/**
 * 格式化菜单路由的路径
 * @description 该函数主要完成以下功能：
 * 1. 标准化路由路径，确保路径格式的一致性
 * 2. 构建完整的路由路径，通过组合父路径和当前路径
 * 3. 处理特殊情况：
 *    - 根路径的特殊处理
 *    - 去除多余的斜杠
 *    - 递归处理子路由路径
 * @param router 需要处理的路由数组
 * @param parentPath 父级路由的路径
 * @returns 处理后的路由数组，包含完整的路径信息
 */
export const menuRouterFormat = (
  router: RouteRecordRaw[],
  parentPath: string
): RouteRecordRaw[] => {
  return router.map(item => {
    // 移除开头和结尾的斜杠，然后重新组合路径
    const cleanParentPath = parentPath.replace(/^\/+|\/+$/g, '')
    const cleanItemPath = item.path.replace(/^\/+|\/+$/g, '')

    // 如果是根路径，直接使用 item.path
    if (parentPath === '/') {
      item.path = `/${cleanItemPath}`
    } else {
      // 否则组合父路径和当前路径
      item.path = `/${cleanParentPath}/${cleanItemPath}`.replace(/\/+/g, '/')
    }

    if (item.children) {
      item.children = menuRouterFormat(item.children, item.path)
    }

    return item
  })
}
