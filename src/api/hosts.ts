import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  HostsListParams,
  HostsListResponse,
  HostsRecord
} from '@/types/hosts'

// 获取主机列表
export const reqGetHostsList = (
  params: HostsListParams
): Promise<ApiResponse<HostsListResponse>> =>
  request.get('/hosts/list', { params })

// 添加主机
export const reqAddHosts = (data: HostsRecord): Promise<ApiResponse<null>> =>
  request.post('/hosts/create', data)

// 编辑主机
export const reqUpdateHosts = (data: HostsRecord): Promise<ApiResponse<null>> =>
  request.put('/hosts/update', data)

// 删除主机
export const reqDeleteHosts = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/hosts/delete', { params: { id } })

// 获取操作系统组
export const reqGetOsGroups = (): Promise<ApiResponse<string[]>> =>
  request.get('/hosts/os-groups')

// 获取主机位置组
export const reqGetLocationGroups = (): Promise<ApiResponse<string[]>> =>
  request.get('/hosts/location-groups')

// 获取主机详情
export const reqGetHostDetail = (
  id: string
): Promise<ApiResponse<HostsRecord>> =>
  request.get('/hosts/detail', { params: { id } })

// 获取主机监控图表数据
