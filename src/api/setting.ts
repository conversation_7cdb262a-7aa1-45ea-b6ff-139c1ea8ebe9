import type { ApiResponse } from '@/types/global'
import type { LDAPConfig, EmailConfig } from '@/types/setting'
import request from '@/utils/request'

// 获取LDAP配置
export function reqGetLDAPConfig(): Promise<ApiResponse<LDAPConfig>> {
  return request.get('/settings/get-ldap')
}

// 测试LDAP连接
export function reqTestLDAPConnection(
  data: LDAPConfig
): Promise<ApiResponse<null>> {
  return request.post('/settings/test-ldap', data)
}

// 保存LDAP配置
export function reqSaveLDAPConfig(
  data: LDAPConfig
): Promise<ApiResponse<null>> {
  return request.post('/settings/save-ldap', data)
}

// 同步LDAP用户
export function reqSyncLDAPUsers(): Promise<ApiResponse<null>> {
  return request.get('/settings/sync-ldap-users')
}

// 保存Email配置
export function reqSaveEmailConfig(
  data: EmailConfig
): Promise<ApiResponse<null>> {
  return request.post('/settings/save-mail', data)
}

// 测试Email连接
export function reqTestEmailConnection(
  data: EmailConfig
): Promise<ApiResponse<null>> {
  return request.post('/settings/test-mail', data)
}

// 获取邮件配置
export function reqGetEmailConfig(): Promise<ApiResponse<EmailConfig>> {
  return request.get('/settings/get-mail')
}
