import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  IpAddressRecord,
  IpAddressListParams,
  IpAddressListResponse
} from '@/types/ipaddress'

// 获取IP地址列表
export const reqGetIpAddressList = (
  params: IpAddressListParams
): Promise<ApiResponse<IpAddressListResponse>> =>
  request.get('/ip-addresses/list', { params })

// 添加IP地址
export const reqAddIpAddress = (
  data: IpAddressRecord
): Promise<ApiResponse<null>> => request.post('/ip-addresses/create', data)

// 更新IP地址
export const reqUpdateIpAddress = (
  data: IpAddressRecord
): Promise<ApiResponse<null>> => request.put('/ip-addresses/update', data)

// 删除IP地址
export const reqDeleteIpAddress = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/ip-addresses/delete', { params: { id } })
