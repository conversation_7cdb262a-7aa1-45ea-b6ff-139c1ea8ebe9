import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  HardwareRecord,
  HardwareListParams,
  HardwareListResponse,
  HardwareInputGroup
} from '@/types/hardware'

// 获取硬件列表
export const reqGetHardwareList = (
  params: HardwareListParams
): Promise<ApiResponse<HardwareListResponse>> =>
  request.get('/hardwares/list', { params })

// 添加硬件
export const reqAddHardware = (
  data: HardwareRecord
): Promise<ApiResponse<null>> => request.post('/hardwares/create', data)

// 删除硬件
export const reqDeleteHardware = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/hardwares/delete', { params: { id } })

// 编辑硬件
export const reqUpdateHardware = (
  data: HardwareRecord
): Promise<ApiResponse<null>> => request.put('/hardwares/update', data)

// 获取硬件输入提示项
export const reqGetHardwareInputGroup = (): Promise<
  ApiResponse<HardwareInputGroup>
> => request.get('/hardwares/input-group')
