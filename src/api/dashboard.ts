import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type { NavRecord, DashboardRecord } from '@/types/dashboard'

// 获取导航列表
export const reqGetNavList = (): Promise<ApiResponse<any>> =>
  request.get('/dashboard/nav/list')

// 添加快捷导航
export const reqAddNav = (data: NavRecord): Promise<ApiResponse<null>> =>
  request.post('/dashboard/nav/create', data)

// 删除快捷导航
export const reqDeleteNav = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/dashboard/nav/delete', { params: { id } })

// 获取仪表盘数据
export const reqGetDashboard = (): Promise<ApiResponse<DashboardRecord>> =>
  request.get('/dashboard/list')
