import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type { ApiRecord, ApiListParams, ApiListResult } from '@/types/api'

// 获取API列表
export const reqGetApiList = (
  params: ApiListParams
): Promise<ApiResponse<ApiListResult>> => request.get('/apis/list', { params })

// 添加API
export const reqAddApi = (data: ApiRecord): Promise<ApiResponse<null>> =>
  request.post('/apis/create', data)

// 删除API
export const reqDeleteApi = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/apis/delete', { params: { id } })

// 编辑API
export const reqUpdateApi = (data: ApiRecord): Promise<ApiResponse<null>> =>
  request.put('/apis/update', data)

// 获取接口分组列表
export const reqGetApiGroups = (): Promise<ApiResponse<{ groups: string[] }>> =>
  request.get('/apis/groups')
