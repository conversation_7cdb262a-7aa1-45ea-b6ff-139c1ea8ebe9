import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  AnnouncementRecord,
  AnnouncementListParams,
  AnnouncementListResponse
} from '@/types/announcement'

// 获取公告列表
export const reqGetAnnouncementList = (
  params: AnnouncementListParams
): Promise<ApiResponse<AnnouncementListResponse>> =>
  request.get('/announcements/list', { params })

// 添加公告
export const reqAddAnnouncement = (
  data: AnnouncementRecord
): Promise<ApiResponse<null>> => request.post('/announcements/create', data)

// 删除公告
export const reqDeleteAnnouncement = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/announcements/delete', { params: { id } })

// 编辑公告
export const reqUpdateAnnouncement = (
  data: AnnouncementRecord
): Promise<ApiResponse<null>> => request.put('/announcements/update', data)

// 启用/禁用公告
export const reqUpdateAnnouncementStatus = (
  id: string,
  status: 'enable' | 'disable'
): Promise<ApiResponse<null>> =>
  request.put('/announcements/toggle-status', { id, status })

// 获取显示的公告
export const reqGetDisplayAnnouncement = (): Promise<
  ApiResponse<AnnouncementRecord>
> => request.get('/announcements/current')
