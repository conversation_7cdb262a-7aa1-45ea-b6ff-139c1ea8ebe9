import type { ApiResponse } from '@/types/global'
import type { MenuListParams, MenuListResult, MenuRecord } from '@/types/menu'
import request from '@/utils/request'

/**
 * 获取菜单列表
 * @param params 查询参数
 * @returns 菜单列表
 */
export const reqGetMenuList = (
  params: MenuListParams
): Promise<ApiResponse<MenuListResult>> => {
  return request.get('/menus/list', { params })
}

/**
 * 添加菜单
 * @param data 菜单数据
 * @returns 空
 */
export const reqAddMenu = (data: MenuRecord): Promise<ApiResponse<null>> => {
  return request.post('/menus/create', data)
}

/**
 * 更新菜单
 * @param data 菜单数据
 * @returns 空
 */
export const reqUpdateMenu = (data: MenuRecord): Promise<ApiResponse<null>> => {
  return request.put('/menus/update', data)
}

/**
 * 删除菜单
 * @param id 菜单ID
 * @returns 空
 */
export const reqDeleteMenu = (id: string): Promise<ApiResponse<null>> => {
  return request.delete('/menus/delete', { params: { id } })
}
