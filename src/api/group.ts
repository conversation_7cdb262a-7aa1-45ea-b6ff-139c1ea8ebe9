// 用户相关接口
import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  GroupRecord,
  GroupListParams,
  GroupListResponse
} from '@/types/group'

// 获取组列表
export const reqGetGroupList = (
  params: GroupListParams
): Promise<ApiResponse<GroupListResponse>> =>
  request.get('/groups/list', { params })

// 添加组
export const reqAddGroup = (data: GroupRecord): Promise<ApiResponse<null>> =>
  request.post('/groups/create', data)

// 删除组
export const reqDeleteGroup = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/groups/delete', { params: { id } })

// 编辑组
export const reqUpdateGroup = (data: GroupRecord): Promise<ApiResponse<null>> =>
  request.put('/groups/update', data)

//更新组关联用户
export const reqUpdateGroupUsers = (
  groupId: string,
  userIds: string[]
): Promise<ApiResponse<null>> =>
  request.put('/groups/update-users', {
    group_id: groupId,
    user_ids: userIds
  })

// 更新组关联的主机
export const reqUpdateGroupHosts = (
  groupId: string,
  hostIds: string[]
): Promise<ApiResponse<null>> =>
  request.put(`/groups/update-hosts`, { group_id: groupId, host_ids: hostIds })
