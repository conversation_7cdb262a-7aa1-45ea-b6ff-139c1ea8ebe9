import type { ApiResponse } from '@/types/global'
import type { RoleListParams, RoleRecord, RoleListResponse } from '@/types/role'
import request from '@/utils/request'

// 获取角色列表
export function reqGetRoleList(
  params: RoleListParams
): Promise<ApiResponse<RoleListResponse>> {
  return request.get('/roles/list', { params })
}

// 添加角色
export const reqAddRole = (data: RoleRecord): Promise<ApiResponse<null>> =>
  request.post('/roles/create', data)

// 删除角色
export const reqDeleteRole = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/roles/delete', { params: { id } })

// 编辑角色
export const reqUpdateRole = (data: RoleRecord): Promise<ApiResponse<null>> =>
  request.put('/roles/update', data)

// 更新角色关联用户
export const reqUpdateRoleUsers = (
  roleId: string,
  userIds: string[]
): Promise<ApiResponse<null>> =>
  request.put('/roles/update-users', {
    role_id: roleId,
    user_ids: userIds
  })
