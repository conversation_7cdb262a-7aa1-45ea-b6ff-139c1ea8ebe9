import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  UserListParams,
  UserListResponse,
  UserRecord,
  UserInfo
} from '@/types/user'

// 获取用户列表
export const reqGetUserList = (
  params: UserListParams
): Promise<ApiResponse<UserListResponse>> =>
  request.get('/users/list', { params })

// 获取用户信息
export const reqGetUserInfo = (): Promise<ApiResponse<UserInfo>> =>
  request.get('/users/info')

// 添加用户
export const reqAddUser = (data: UserRecord): Promise<ApiResponse<null>> =>
  request.post('/users/create', data)

// 编辑用户
export const reqUpdateUser = (data: UserRecord): Promise<ApiResponse<null>> =>
  request.put('/users/update', data)

// 删除用户
export const reqDeleteUser = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/users/delete', { params: { id } })

// 重置密码
export const reqResetPassword = (
  id: string,
  password: string
): Promise<ApiResponse<null>> =>
  request.put('/users/reset-password', { id, password })
