import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type {
  TicketRecord,
  Attachment,
  TicketListResult,
  TicketListParams,
  UpdateTicketUsersParams
} from '@/types/ticket'

// 创建工单
export const reqCreateTicket = (
  data: TicketRecord
): Promise<ApiResponse<null>> => request.post('/ticket/create', data)

// 上传工单附件
export const reqUploadAttachment = (
  file: File
): Promise<ApiResponse<Attachment>> => {
  const formData = new FormData()
  formData.append('file', file, file.name)

  return request.post('/ticket/upload-attachment', formData, {
    headers: {
      'Content-Type': undefined
    }
  })
}

// 删除工单附件
export const reqDeleteAttachment = (
  filePath: string
): Promise<ApiResponse<null>> => {
  return request.delete('/ticket/delete-attachment', {
    params: { file_path: filePath }
  })
}

// 下载工单附件
export const reqDownloadAttachment = (
  filePath: string,
  fileName: string
): Promise<any> => {
  return request.get('/ticket/download-attachment', {
    params: { file_path: filePath, file_name: fileName },
    responseType: 'blob',
    headers: {
      Accept: 'application/octet-stream'
    }
  })
}

// 获取工单列表
export const reqGetTicketList = (
  params: TicketListParams
): Promise<ApiResponse<TicketListResult>> =>
  request.get('/ticket/list', { params })

// 删除工单
export const reqDeleteTicket = (id: string): Promise<ApiResponse<null>> =>
  request.delete('/ticket/delete', { params: { id } })

// 更新工单处理人
export const reqUpdateTicketUsers = (
  data: UpdateTicketUsersParams
): Promise<ApiResponse<null>> => request.post('/ticket/update-users', data)

// 更新工单
export function reqUpdateTicket(data: {
  id: string
  type: 'comment' | 'approval' | 'handle' | 'revoke'
  comments?: string
  decision?: 'approved' | 'rejected' | 'revoke'
  username: string
}) {
  return request.post<ApiResponse<null>>('/ticket/update', data)
}
