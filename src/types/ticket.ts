import type { PageParams } from './global'
// 工单详情
export interface TicketRecord {
  id?: string // 可选的工单 ID
  title: string // 工单标题
  progress: '1' | '2' | '3' | '4' | '5' | '6' // 当前进度
  type: string // 工单类型
  submitter: string // 提交人
  assignee: string // 指派人
  attachments: Attachment[] // 附件数组
  description: string // 描述信息
  create_time?: string
  finish_time?: string
  timeline?: TimelineItem[] //已完成的事项
  ticket_users?: TicketUsers[] // 工单处理人
}

interface TimelineItem {
  action: string // 操作，如"审批"、"指派"、"完成"
  user: string
  time: string
  comments?: string // 可选评论
  decision?: 'approved' | 'rejected' | 'revoke'
}

// 定义附件类型
export interface Attachment {
  file_name: string
  src: string
}

// 工单列表响应
export interface TicketListResult {
  tickets: TicketRecord[]
  total: number
}

export interface TicketListParams extends PageParams {
  title?: string
  type?: string
  submitter?: string
  assignee?: string
  progress?: string
  view_type?: string
}

// 更新工单处理人
export interface UpdateTicketUsersParams {
  id: string
  users: TicketUsers[]
}

interface TicketUsers {
  type: '审批人' | '处理人'
  user_name: string
  status: '未处理' | '已处理'
}

// 工单类型配置
export const ticketTypeConfig = {
  computer: {
    label: '申请服务器资源',
    value: 'computer'
  },
  objectStorage: {
    label: '申请对象存储资源',
    value: 'objectStorage'
  },
  openPort: {
    label: '申请服务器端口开通',
    value: 'openPort'
  },
  account: {
    label: '远程运维人员账号相关',
    value: 'account'
  },
  loadBalancing: {
    label: '负载均衡相关',
    value: 'loadBalancing'
  },
  ipaddress: {
    label: '申请地址映射相关流程',
    value: 'ipaddress'
  },
  domain: {
    label: '申请域名相关流程',
    value: 'domain'
  },
  scanning: {
    label: '漏洞扫描备案',
    value: 'scanning'
  },
  issue: {
    label: '其他问题处理',
    value: 'issue'
  }
} as const

export type TicketType = keyof typeof ticketTypeConfig
