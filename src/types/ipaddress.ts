// IP 地址相关类型定义
import type { PageParams } from './global'

// IP 地址记录
export interface IpAddressRecord {
  id?: string
  internet_ip: string // 互联网 IP 地址
  internet_port: string[] // 互联网端口
  mapped_ip: string // 映射 IP 地址
  mapped_port: string[] // 映射端口
  operator: string // 运营商
  description: string // 描述
  status: 'enable' | 'disable' // 状态
}

// IP 地址列表查询参数
export interface IpAddressListParams extends PageParams {
  internet_ip?: string
  mapped_ip?: string
  operator?: string
  status?: 'enable' | 'disable'
  internet_port?: string // 互联网端口
  mapped_port?: string // 映射端口
}

// IP 地址列表响应
export interface IpAddressListResponse {
  ip_address: IpAddressRecord[]
  total: number
}
