// 公告相关类型定义
import type { PageParams } from './global'

export interface AnnouncementRecord {
  id?: string
  content: string
  start_time: string
  end_time: string
  status: 'enable' | 'disable'
}

export interface AnnouncementListParams extends PageParams {
  content?: string
  start_time?: string
  end_time?: string
  status?: 'enable' | 'disable'
}

export interface AnnouncementListResponse {
  announcements: AnnouncementRecord[]
  total: number
}
