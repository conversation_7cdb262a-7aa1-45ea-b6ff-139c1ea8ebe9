import type { PageParams } from './global'

// BASH 审计命令记录
export interface BashAuditRecord {
  host_name: string // 主机名
  timestamp: string // 时间,"2025-01-09 14:46:21"
  terminal: string // 终端
  working_directory: string // 工作目录
  host_ip: string // 主机 IP
  username: string // 用户名
  executed_command: string // 执行命令
  source_ip: string // 源 IP
  system_user: string // 系统用户
}

// BASH 审计命令记录列表
export interface BashAuditRecordList {
  bash_audits: BashAuditRecord[]
  total: number
}

// BASH 审计命令记录查询参数
export interface BashAuditRecordQuery extends PageParams {
  start_time?: string
  end_time?: string
  host_name?: string
  username?: string
  executed_command?: string
  host_ip?: string
}
