// 硬件相关类型定义
import type { PageParams } from './global'

// 硬件记录
export interface HardwareRecord {
  id?: string
  name: string // 硬件名称
  model: string // 硬件型号
  type: 'server' | 'network' | 'safety' | 'storage' | 'other' // 硬件类型
  location: string // 硬件位置
  rack_location: string // 机架位置
  size: string // 硬件大小
  manager_address: string // 管理地址 IP 形式。
  business_address: string // 业务地址字符串 如 ***********:8080
  manufacturer: string // 制造商
  purchase_date: string // 购买日期
  warranty_date: string // 保修日期
  responsible_person: string // 责任人
  description: string // 描述
  status: 'enable' | 'disable' // 状态
}

// 硬件列表查询参数
export interface HardwareListParams extends PageParams {
  name?: string
  manager_address?: string
  business_address?: string
  location?: string
  type?: 'server' | 'network' | 'safety' | 'storage' | 'other'
  status?: 'enable' | 'disable'
}

// 硬件列表响应
export interface HardwareListResponse {
  hardwares: HardwareRecord[]
  total: number
}

// 硬件输入提示项
export interface HardwareInputGroup {
  locations: string[]
  responsible_persons: string[]
  sizes: string[]
}
