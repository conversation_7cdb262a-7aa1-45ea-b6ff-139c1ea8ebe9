import type { PageParams } from './global'

// 用户记录接口
export interface UserRecord {
  id?: string // 用户ID（可选）
  username: string // 用户名
  loginname: string // 登录名
  phone?: string // 电话号码（可选）
  email?: string // 电子邮件（可选）
  idcard?: string // 身份证号（可选）
  auth_mode: string // 认证模式
  status: 'enable' | 'disable' // 用户状态：启用或禁用
  groups?: string[] // 用户所属组（可选）
  roles?: string[] // 用户角色（可选）
  password?: string // 密码（可选）
  created_time?: string // 创建时间（可选）
  last_login_time?: string // 最后登录时间（可选）
}

// 扩展用户信息接口，包含组名、角色名和权限信息
export interface UserInfo extends UserRecord {
  permissions: string[] // 权限列表
  role_names: string[] // 角色名称列表
}

// 用户列表响应接口
export interface UserListResponse {
  users: UserRecord[]
  total: number
}

// 用户列表请求参数接口
export interface UserListParams extends PageParams {
  username?: string
  loginname?: string
  phone?: string
  email?: string
  status?: string
  groups?: string
  roles?: string
  auth_mode?: string
}
