import type { PageParams } from './global'

// 菜单记录接口
export interface MenuRecord {
  id?: string
  name: string
  identifier: string
  type: 'menu' | 'button'
  parent_id: string | null
  children?: MenuRecord[]
}

// 查询参数接口
export interface MenuListParams extends PageParams {
  name?: string
  identifier?: string
  type?: 'menu' | 'button'
}

// 列表结果接口
export interface MenuListResult {
  menus: MenuRecord[]
  total: number
}
