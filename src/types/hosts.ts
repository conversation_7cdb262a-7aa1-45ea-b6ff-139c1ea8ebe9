import type { PageParams } from './global'

export interface HostsRecord {
  id?: string
  hostname: string
  hardware_type: string
  ip: string[]
  os: string
  cpu_total: number | undefined
  memory_total: number | undefined
  disk_total: number | undefined
  location: string
  groups: string[]
  status: string
  description?: string
  created_time?: string
  updated_time?: string
  agent_metrics?: AgentMetricRecord
}

export interface HostsListParams extends PageParams {
  hostname?: string
  ip?: string
  os?: string
  location?: string
  groups?: string[]
  status?: string
  sort_by?: string
  sort_order?: string
}

/** 监控数据结构体 */
export interface AgentMetricRecord {
  id: string
  last_update_time: number
  base_info: BaseInfo
  cpu_info: CPUInfo
  mem_info: MemInfo
  default_net_info: DefaultNetInfo
  conn_info: ConnInfo
  total_disk_info: TotalDiskInfo
  disk_info: DiskInfo[]
  partition_info: PartitionInfo[]
  net_info: NetInfo[]
}

/** 基础信息结构体 */
export interface BaseInfo {
  hostname: string
  boot_time: number
  arch: string
  load_avg: [number, number, number]
  kernel: string
}

/** CPU信息结构体 */
export interface CPUInfo {
  model: string
  total: number
  used_percent: number
}

/** 内存信息结构体 */
export interface MemInfo {
  total: number
  used: number
  used_percent: number
}

/** 总磁盘信息结构体 */
export interface TotalDiskInfo {
  total: number
}

/** 磁盘信息结构体 */
export interface DiskInfo {
  disk_type: string
  device_name: string
  file_system: string
  total: number
  used: number
  free: number
  used_percent: number
}

/** 分区信息结构体 */
export interface PartitionInfo {
  mount_point: string
  total: number
  used: number
  used_percent: number
}

/** 默认网络信息结构体 */
export interface DefaultNetInfo {
  net_in_speed: number
  net_in_transfer: number
  net_out_speed: number
  net_out_transfer: number
}

/** 网络信息结构体 */
export interface NetInfo {
  interface_name: string
  net_in_speed: number
  net_in_transfer: number
  net_out_speed: number
  net_out_transfer: number
}

/** 连接信息结构体 */
export interface ConnInfo {
  process_count: number
  tcp_conn_count: number
  udp_conn_count: number
}

export interface HostsListResponse {
  hosts: HostsRecord[]
  total: number
}
