// constantRoutes.ts 定义所有的常量路由信息,也就是所有不需要权限的路由
import type { RouteRecordRaw } from 'vue-router'
import dashboardIcon from '~icons/material-symbols-light/dashboard'
import { menuRouterFormat } from '@/utils/route'
import { markRaw } from 'vue'

// 静态路由
const constantRoutes: RouteRecordRaw[] = [
  {
    path: 'login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    // 默认首页
    path: '/',
    name: 'layout',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '首页',
      hidden: false
    },
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '工作台',
          icon: markRaw(dashboardIcon),
          hidden: false
        }
      }
    ]
  },
  {
    // 404
    path: '404',
    name: '404',
    component: () => import('@/views/error/404Page.vue'),
    meta: {
      title: '404',
      hidden: true
    }
  }
]

// 任意路由
export const anyRoutes: RouteRecordRaw = {
  path: '/:pathMatch(.*)*',
  component: () => import('@/views/error/404Page.vue'),
  meta: { title: '404', hidden: true }
}

export const constantRoutesFormat = menuRouterFormat(constantRoutes, '')
