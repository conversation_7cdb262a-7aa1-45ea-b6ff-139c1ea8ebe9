// dynamicRoutes.ts 定义所有的动态路由信息,也就是所有需要权限的路由
import type { RouteRecordRaw } from 'vue-router'
import IconSystem from '~icons/material-symbols/code-blocks-outline'
import IconUser from '~icons/mdi/user'
import IconGroup from '~icons/mdi/account-group'
import IconMenu from '~icons/material-symbols/menu'
import IconRole from '~icons/ri/admin-fill'
import IconApi from '~icons/tabler/api'
import IconSetting from '~icons/weui/setting-filled'
import IconAssets from '~icons/ic/baseline-dvr'
import IconHosts from '~icons/uil/server'
import IconAnnouncement from '~icons/mingcute/announcement-line'
import IconHardware from '~icons/vaadin/server'
import IconBashAudit from '~icons/logos/bash-icon'
import IconIpAddress from '~icons/oui/ip'
import IconTicket from '~icons/lsicon/work-order-appointment-outline'
import IconTicketCreate from '~icons/gridicons/create'
import IconTicketMy from '~icons/solar/pin-list-linear'
import IconTicketAll from '~icons/icon-park-outline/list-fail'
import IconView from '~icons/wpf/statistics'

import { markRaw } from 'vue'
import { menuRouterFormat } from '@/utils/route'
const dynamicRoutes: RouteRecordRaw[] = [
  {
    path: 'assets',
    name: 'Assets',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '资产中心',
      hidden: false,
      icon: markRaw(IconAssets)
    },
    children: [
      {
        path: 'hostsAssets',
        name: 'HostsAssets',
        meta: {
          title: '主机资产',
          hidden: false,
          icon: markRaw(IconHosts)
        },
        component: () => import('@/views/assets/hosts.vue')
      },
      {
        path: 'HostDetail/:id',
        name: 'HostDetail',
        meta: {
          title: '主机详情',
          hidden: true,
          icon: markRaw(IconHosts)
        },
        component: () => import('@/views/assets/hostDetail.vue')
      },
      {
        path: 'hardwareAssets',
        name: 'HardwareAssets',
        meta: {
          title: '硬件资产',
          hidden: false,
          icon: markRaw(IconHardware)
        },
        component: () => import('@/views/assets/Hardware.vue')
      },
      {
        path: 'ipaddressAssets',
        name: 'IpaddressAssets',
        meta: {
          title: 'IP地址资产',
          hidden: false,
          icon: markRaw(IconIpAddress)
        },
        component: () => import('@/views/assets/ipaddress.vue')
      }
    ]
  },
  {
    path: 'ticket',
    name: 'Ticket',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '工单中心',
      hidden: false,
      icon: markRaw(IconTicket)
    },
    children: [
      {
        path: 'createTicket',
        name: 'CreateTicket',
        meta: {
          title: '创建工单',
          hidden: false,
          icon: markRaw(IconTicketCreate)
        },
        component: () => import('@/views/ticket/CreateTicket.vue')
      },
      {
        path: 'myTicket',
        name: 'MyTicket',
        meta: {
          title: '与我相关',
          hidden: false,
          icon: markRaw(IconTicketMy)
        },
        component: () => import('@/views/ticket/MyTicket.vue')
      },
      {
        path: 'allTicket',
        name: 'AllTicket',
        meta: {
          title: '所有工单',
          hidden: false,
          icon: markRaw(IconTicketAll)
        },
        component: () => import('@/views/ticket/AllTicket.vue')
      }
    ]
  },
  {
    path: 'audit',
    name: 'Audit',
    component: () => import('@/layout/DefaultLayout.vue'),
    redirect: '/audit/bashAudit',
    meta: {
      title: '审计中心',
      hidden: false,
      icon: markRaw(IconAssets)
    },
    children: [
      {
        path: 'bashAudit',
        name: 'BashAudit',
        meta: {
          title: 'BASH审计',
          hidden: false,
          icon: markRaw(IconBashAudit)
        },
        component: () => import('@/views/audit/bashAudit.vue')
      }
    ]
  },
  // 经济户籍最新数据
  {
    path: 'dataView',
    name: 'DataView',
    component: () => import('@/layout/DefaultLayout.vue'),
    redirect: '/dataView/dataInfoOnJjhj',
    meta: {
      title: '数据展示',
      hidden: false,
      icon: shallowRef(IconView)
    },
    children: [
      {
        path: 'dataInfoOnJjhj',
        name: 'DataInfoOnJjhj',
        meta: {
          title: '经户实时数据',
          icon: shallowRef(IconView),
          hidden: false
        },
        component: () => import('@/views/dataview/dataview.vue')
      }
    ]
  },
  {
    path: 'system',
    name: 'System',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '系统管理',
      hidden: false,
      icon: markRaw(IconSystem)
    },
    children: [
      {
        path: 'userCenter',
        name: 'UserCenter',
        meta: {
          title: '用户中心',
          hidden: false,
          icon: markRaw(IconUser)
        },
        component: () => import('@/views/system/UserCenter.vue')
      },
      {
        path: 'roleCenter',
        name: 'RoleCenter',
        meta: {
          title: '角色中心',
          hidden: false,
          icon: markRaw(IconRole)
        },
        component: () => import('@/views/system/RoleCenter.vue')
      },
      {
        path: 'menuCenter',
        name: 'MenuCenter',
        meta: {
          title: '菜单中心',
          hidden: false,
          icon: markRaw(IconMenu)
        },
        component: () => import('@/views/system/MenuCenter.vue')
      },
      {
        path: 'apiCenter',
        name: 'ApiCenter',
        meta: {
          title: 'API中心',
          hidden: false,
          icon: markRaw(IconApi)
        },
        component: () => import('@/views/system/ApiCenter.vue')
      },
      {
        path: 'groupCenter',
        name: 'GroupCenter',
        meta: {
          title: '组中心',
          hidden: false,
          icon: markRaw(IconGroup)
        },
        component: () => import('@/views/system/GroupCenter.vue')
      },
      {
        path: 'announcement',
        name: 'Announcement',
        meta: {
          title: '公告中心',
          hidden: false,
          icon: markRaw(IconAnnouncement)
        },
        component: () => import('@/views/system/Announcement.vue')
      },
      {
        path: 'settingCenter',
        name: 'SettingCenter',
        meta: {
          title: '系统设置',
          hidden: false,
          icon: markRaw(IconSetting)
        },
        component: () => import('@/views/system/SettingCenter.vue')
      }
    ]
  }
]

export const dynamicRoutesFormat = menuRouterFormat(dynamicRoutes, '')
