<script setup lang="ts">
import { useUserStore } from '@/stores'

const userStore = useUserStore()

const handleLogout = async () => {
  await AMessage.success('退出登录成功')
  userStore.Logout()
}
</script>
<template>
  <div class="user-info" v-if="userStore.userInfo">
    <a-dropdown trigger="hover">
      <a-typography-text class="username">
        {{ userStore.userInfo?.username || '未知用户' }}
      </a-typography-text>
      <template #content>
        <a-doption>
          <a-space @click="handleLogout">
            <icon-material-symbols-logout />
            <span> 退出登录 </span>
          </a-space>
        </a-doption>
      </template>
    </a-dropdown>
  </div>
</template>

<style lang="scss" scoped>
.user-info {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--color-fill-2);
  }

  .username {
    font-weight: 600;
    cursor: pointer;
  }
}
</style>
