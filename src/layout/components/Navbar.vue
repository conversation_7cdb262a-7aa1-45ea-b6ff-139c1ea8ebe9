<template>
  <div class="navbar">
    <div class="navbar__left">
      <slot name="left" />
    </div>
    <div class="navbar__center">
      <slot />
      <slot name="center" />
    </div>
    <div class="navbar__right">
      <slot name="right" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 0 20px;
  box-sizing: border-box;

  &__left {
    height: 100%;
    display: flex;
  }

  &__center {
    height: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }

  &__right {
    height: 100%;
    display: flex;
    flex-shrink: 0;
    align-items: center;
  }
}
</style>
