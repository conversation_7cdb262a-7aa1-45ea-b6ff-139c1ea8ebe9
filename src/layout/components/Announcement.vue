<template>
  <div v-if="announcement" class="announcement">
    <!-- 公告标签区域 -->
    <div class="announcement__label">
      <icon-mingcute-announcement-line class="announcement__icon" />
    </div>
    <!-- 公告内容滚动区域 -->
    <div class="announcement__content">
      <div
        class="announcement__scroll"
        :style="{ transform: `translateX(${-scrollPosition}px)` }"
      >
        <span>{{ announcement }}</span>
        <!-- 当需要滚动时显示第二段文本 -->
        <span v-if="needScroll" class="announcement__separator">
          {{ announcement }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { reqGetDisplayAnnouncement } from '@/api/announcement'
import type { AnnouncementRecord } from '@/types/announcement'

// 公告内容 - 如果没有内容则不显示组件
const announcement = ref('')

// 滚动相关的响应式变量
const scrollPosition = ref(0) // 当前滚动位置
const contentWidth = ref(0) // 单段文本内容宽度
const containerWidth = ref(0) // 容器宽度
const needScroll = ref(false) // 是否需要滚动标记
let animationFrame: number // 动画帧ID

// 获取当前显示的公告
const fetchAnnouncement = async () => {
  try {
    const res = await reqGetDisplayAnnouncement()
    const currentAnnouncement = res.data

    // console.log('currentAnnouncement', currentAnnouncement)
    // 只有启用状态的公告才显示
    if (currentAnnouncement && currentAnnouncement.status === 'enable') {
      announcement.value = currentAnnouncement.content
      // 获取到公告后初始化滚动
      nextTick(() => {
        initScroll()
      })
    } else {
      announcement.value = ''
    }
  } catch (error) {
    console.error('获取公告失败:', error)
    announcement.value = ''
  }
}

// 检查是否需要滚动并初始化滚动
const initScroll = () => {
  const contentEl = document.querySelector(
    '.announcement__scroll span:first-child'
  ) as HTMLElement
  const containerEl = document.querySelector(
    '.announcement__content'
  ) as HTMLElement

  if (contentEl && containerEl) {
    // 计算内容和容器宽度
    contentWidth.value = contentEl.offsetWidth
    containerWidth.value = containerEl.offsetWidth

    // 判断是否需要滚动（内容宽度大于容器宽度）
    needScroll.value = contentWidth.value > containerWidth.value

    // 如果需要滚动则启动动画
    if (needScroll.value) {
      if (!animationFrame) {
        animationFrame = requestAnimationFrame(scroll)
      }
    } else {
      // 如果不需要滚动，重置位置并停止动画
      scrollPosition.value = 0
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
        animationFrame = 0
      }
    }
  }
}

// 处理窗口大小改变
const handleResize = () => {
  initScroll()
}

// 文本滚动逻辑
const scroll = () => {
  if (needScroll.value) {
    // 持续滚动
    scrollPosition.value += 0.5
    // 当第一段文本完全滚出（包括间距）后重置位置
    if (scrollPosition.value >= contentWidth.value + 100) {
      scrollPosition.value = 0
    }
    animationFrame = requestAnimationFrame(scroll)
  }
}

onMounted(async () => {
  // 替换原来的模拟数据，改用API获取公告
  await fetchAnnouncement()

  // 添加 resize 事件监听
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
  // 移除 resize 事件监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.announcement {
  margin-left: 60px;
  height: 32px;
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--color-neutral-2);
  padding: 0 16px;
  border-radius: 5px;

  // 公告标签样式
  &__label {
    font-weight: 600;
    font-size: 15px;
    margin-right: 16px;
    color: var(--color-text-1);
    flex-shrink: 0;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  // 公告图标样式
  &__icon {
    color: #2f88ff;
    font-size: 18px;
    display: flex;
    align-items: center;
  }

  // 公告内容容器样式
  &__content {
    flex: 1;
    overflow: hidden; // 超出部分隐藏
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
  }

  // 滚动文本容器样式
  &__scroll {
    white-space: nowrap; // 文本不换行
    position: absolute;
    color: rgb(var(--orange-7));
    font-weight: 500;
    line-height: 1;
    display: flex;
    gap: 100px; // 两段文本之间的间距
  }

  // 第二段文本样式
  &__separator {
    display: inline-block;
  }
}
</style>
