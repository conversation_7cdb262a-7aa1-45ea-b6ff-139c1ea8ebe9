<script setup lang="ts">
import type { RouteRecordRaw } from 'vue-router'
import MenuItem from './MenuItem.vue'
import useUserStore from '@/stores/modules/user'
import { formatRoutes } from '@/utils/route'
const userStore = useUserStore()
const menuList = ref<RouteRecordRaw[]>(formatRoutes(userStore.menuList))
const router = useRouter()

// 子菜单点击事件
const onClickMenuItem = (key: string): void => {
  router.push(key)
}

const route = useRoute()

// 当前选中菜单
const selectedKeys = computed(() => [route.path])
</script>

<template>
  <div>
    <a-menu
      class="menu"
      auto-open-selected
      :selected-keys="selectedKeys"
      @menuItemClick="onClickMenuItem"
      mode="vertical"
      :accordion="true"
    >
      <MenuItem v-for="menu in menuList" :key="menu.path" :menu="menu" />
    </a-menu>
  </div>
</template>

<style lang="scss" scoped>
.menu.arco-menu-horizontal {
  background-color: var(--color-bg-3);

  :deep(.arco-menu-icon) {
    margin-right: 4px;
    line-height: 1.2;
    flex: none;
    vertical-align: inherit;
  }

  :deep(.arco-menu-pop-header) {
    background-color: transparent;

    &:hover {
      background-color: var(--color-fill-2);
    }
  }
}

.menu {
  :deep(.arco-menu-overflow-wrap) {
    display: flex;
    justify-content: end;
  }
}
</style>
