<script setup lang="ts">
import { toRefs, shallowRef } from 'vue'
import type { PropType } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

// 显式导入自己
import MenuItem from './MenuItem.vue'

const props = defineProps({
  menu: {
    type: Object as PropType<RouteRecordRaw>,
    required: true
  }
})

const { menu } = toRefs(props)
// 使用 shallowRef 处理图标
const icon = shallowRef(menu.value.meta?.icon)
</script>

<template>
  <template v-if="!menu.children">
    <a-menu-item :key="menu.path">
      <template #icon v-if="icon">
        <component :is="icon" />
      </template>
      {{ menu.meta?.title }}
    </a-menu-item>
  </template>

  <a-sub-menu v-else :key="menu.path" :title="menu.meta?.title as string">
    <template #icon v-if="icon">
      <component :is="icon" />
    </template>
    <MenuItem
      v-for="menuChild in menu.children"
      :key="menuChild.path"
      :menu="menuChild"
    />
  </a-sub-menu>
</template>

<style lang="scss" scoped></style>
