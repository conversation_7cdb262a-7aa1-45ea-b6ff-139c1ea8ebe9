<script setup lang="ts">
import { markRaw, reactive, ref } from 'vue'

import fullscreen from '~icons/lucide/fullscreen'
import fullscreenExit from '~icons/mingcute/fullscreen-exit-fill'

import { useFullscreen, useCycleList } from '@vueuse/core'
const { toggle: toggleFullScreen } = useFullscreen()

const currentMode = reactive({
  icon: markRaw(fullscreen),
  title: '正常模式'
})

// 主题模式列表
const modeList = ref([
  {
    icon: markRaw(fullscreen),
    title: '正常模式'
  },
  {
    icon: markRaw(fullscreenExit),
    title: '全屏模式'
  }
])

// 下拉菜单选中事件
const handleSelect = (val: any) => {
  Object.assign(currentMode, val)
  toggleFullScreen()
}

// 初始化一个可以循环切换的列表状态 state 当前值， next 下一个参数
const { state, next } = useCycleList(modeList, {
  initialValue: currentMode
})

// 点击切换触发
const modeNext = () => {
  next()
  Object.assign(currentMode, state.value)
  toggleFullScreen()
}
</script>

<template>
  <a-dropdown @select="handleSelect" trigger="hover" class="mode-dropdown">
    <a-button type="text" @click="modeNext()">
      <template #icon>
        <component :is="currentMode.icon" class="Icon"></component>
      </template>
    </a-button>
    <template #content>
      <a-doption v-for="item of modeList" :key="item.title" :value="item">
        <template #icon v-if="currentMode.title === item.title">
          <icon-material-symbols-check-small class="Icon" />
        </template>
        <template #default>{{ item.title }}</template>
      </a-doption>
    </template>
  </a-dropdown>
</template>

<style scoped>
.mode-dropdown .arco-dropdown-option {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.Icon {
  color: var(--color-text-1);
  font-size: 16px;
}
</style>
