<script setup lang="ts">
import { useSystemStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useCycleList } from '@vueuse/core'
const systemStore = useSystemStore()
const { currentMode, modeList } = storeToRefs(systemStore)

// 初始化模式
systemStore.initMode()

// 下拉菜单选中事件
const handleSelect = (val: any) => (currentMode.value = val)

// 初始化一个可以循环切换的列表状态 state 当前值， next 下一个参数
const { state, next } = useCycleList(modeList, {
  initialValue: currentMode
})

// 点击切换触发
const modeNext = () => {
  next()
  currentMode.value = state.value
}
</script>

<template>
  <a-dropdown @select="handleSelect" trigger="hover" class="mode-dropdown">
    <a-button type="text" @click="modeNext()">
      <template #icon>
        <component :is="currentMode?.icon" class="Icon"></component>
      </template>
    </a-button>
    <template #content>
      <a-doption v-for="item of modeList" :key="item.name" :value="item">
        <template #icon v-if="currentMode?.name === item.name">
          <icon-material-symbols-check-small class="Icon" />
        </template>
        <template #default>{{ item.title }}</template>
      </a-doption>
    </template>
  </a-dropdown>
</template>

<style scoped>
.mode-dropdown .arco-dropdown-option {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.Icon {
  color: var(--color-text-1);
  font-size: 16px;
}
</style>
