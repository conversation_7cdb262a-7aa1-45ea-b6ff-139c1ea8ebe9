<script setup lang="ts">
import Navbar from './components/Navbar.vue'
import Logo from './components/logo.vue'
import Github from './components/Github.vue'
import SwitchScreen from './components/SwitchScreen.vue'
import SwitchMode from './components/SwitchMode.vue'
import Menu from './components/Menu/Menu.vue'
import Footer from './components/Footer.vue'
import UserInfo from './components/UserInfo.vue'
import Announcement from './components/Announcement.vue'
import useUserStore from '@/stores/modules/user'

const userStore = useUserStore()
// 侧边栏收缩状态
const collapsed = ref(false)

// 侧边栏收缩触发事件
const handleCollapse = (val: boolean, type: string) => {
  // const content = type === 'responsive' ? '响应式触发' : '点击触发'
  // console.log(`${content}侧边栏，当前状态：${val}`)
  collapsed.value = val
}
</script>

<template>
  <div class="sidebar-layout">
    <a-watermark :content="`盾山运维平台-${userStore.userInfo?.username}`">
      <a-layout>
        <a-affix>
          <a-layout-header>
            <Navbar>
              <template #left>
                <Logo />
              </template>
              <template #center>
                <Announcement />
              </template>
              <template #right>
                <SwitchScreen />
                <SwitchMode />
                <!-- <Github /> -->
                <UserInfo />
              </template>
            </Navbar>
          </a-layout-header>
        </a-affix>

        <a-layout>
          <a-affix :offsetTop="58">
            <a-layout-sider
              breakpoint="lg"
              :width="220"
              height="calc(100vh-58px)"
              collapsible
              :collapsed="collapsed"
              @collapse="handleCollapse"
            >
              <Menu />
            </a-layout-sider>
          </a-affix>

          <a-layout>
            <a-layout-content>
              <router-view v-slot="{ Component }">
                <transition name="fade-x">
                  <component :is="Component" />
                </transition>
              </router-view>
            </a-layout-content>

            <!-- 注释掉页脚
          <a-layout-footer>
            <Footer />
          </a-layout-footer> 
          -->
          </a-layout>
        </a-layout>
      </a-layout>
    </a-watermark>
  </div>
</template>
<style lang="scss" scoped>
.sidebar-layout {
  :deep(.arco-layout-header),
  :deep(.arco-layout-footer),
  :deep(.arco-layout-content) {
    color: var(--color-text-1);
    font-size: 14px;
  }

  :deep(.arco-layout-header) {
    width: 100%;
    height: 58px;
    background-color: var(--color-bg-3);
    border-bottom: 1px solid var(--color-border-1);
    box-sizing: border-box;
  }

  :deep(.arco-layout-content) {
    padding: 15px;
    background-color: var(--color-neutral-2);
    height: calc(100% - 58px);
  }

  /* 注释掉页脚相关样式
  :deep(.arco-layout-footer) {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid var(--color-border-1);
    box-sizing: border-box;
    background-color: var(--color-neutral-2);
    color: var(--color-text-1);
    font-size: 14px;
  }
  */

  :deep(.arco-layout-sider) {
    height: calc(100vh - 58px);
  }

  :deep(.arco-layout-sider),
  :deep(.arco-layout-sider-trigger) {
    border-right: 1px solid var(--color-border-1);
    box-sizing: border-box;
  }
}
</style>
