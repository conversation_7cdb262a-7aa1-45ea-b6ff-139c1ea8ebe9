import { createApp } from 'vue'
import { VueDataUi } from 'vue-data-ui'
import '@wangeditor/editor/dist/css/style.css'
import '@/assets/base.scss'

import ArcoVue from '@arco-design/web-vue'

import '@arco-design/web-vue/dist/arco.css'

import App from './App.vue'
import router from './router'
import pinia from './stores'

// 引入路由鉴权
import './permission'

const app = createApp(App)
app.component('VueDataUi', VueDataUi)
app.use(pinia)
app.use(router)
app.use(ArcoVue)
app.mount('#app')
