// 导入所需的依赖和组件
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import router from '@/router'
import { useUserStore } from '@/stores'
import { useColorMode } from '@vueuse/core'

// 配置 NProgress 进度条的具体参数
NProgress.configure({
  easing: 'ease', // 动画方式：缓动效果
  speed: 500, // 递增进度条的速度（单位：ms）
  showSpinner: false, // 是否显示加载图标（圆圈动画）
  trickleSpeed: 200, // 自动递增间隔（单位：ms）
  minimum: 0.3 // 初始化时的最小百分比（0-1 之间）
})

// 定义路由白名单（无需登录即可访问的路由）
const whiteList = ['/login', '/404']

// 前置路由守卫：路由跳转前的处理逻辑
router.beforeEach(async (to, from, next) => {
  // 启动进度条
  NProgress.start()
  // 设置页面标题
  document.title = (to.meta.title || '盾山运维平台') as string

  const userStore = useUserStore()
  const hasToken = userStore.token

  if (to.path === '/login') {
    useColorMode({
      attribute: 'arco-theme',
      emitAuto: true,
      selector: 'body',
      initialValue: 'light',
      storageKey: null,
      modes: {
        auto: 'light',
        light: 'light',
        dark: 'dark'
      }
    })
  }

  if (hasToken) {
    // 已登录状态
    if (to.path === '/login') {
      // 如果已登录，访问登录页则重定向到首页
      next('/')
      NProgress.done()
    } else {
      // 判断是否有用户信息
      if (!userStore.userInfo) {
        try {
          // 获取用户信息
          await userStore.GetUserInfo()
          // 重要：使用 replace 模式重新导航，避免导航循环
          next({ ...to, replace: true })
        } catch (error: any) {
          // 修改这里的错误处理
          // 不再显示额外的错误消息，因为 request.ts 中已经显示了 token 过期的提示
          userStore.Logout()
          next('/login')
          NProgress.done()
        }
      } else {
        // 已有用户信息，直接放行
        next()
      }
    }
  } else {
    // 未登录状态
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接放行
      next()
    } else {
      // 不在白名单中，重定向到登录页
      next('/login')
      NProgress.done()
    }
  }
})

// 后置路由守卫：路由跳转完成后的处理逻辑
router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})
